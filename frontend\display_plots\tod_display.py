import streamlit as st
import pandas as pd
from db.fetch_tod_tab_data import (
    fetch_tod_binned_data, 
    fetch_combined_monthly_data,
    fetch_all_daily_tod_data,
    fetch_daily_tod_data,
    fetch_hourly_generation_data
)
from visualizations.tod_tab_visual import (
    create_monthly_before_banking_plot,
    create_monthly_before_banking_plot_interactive,
    create_monthly_banking_settlement_chart,
    create_monthly_banking_settlement_chart_interactive,
    create_tod_binned_plot,
    create_tod_generation_plot,
    create_tod_generation_plot_interactive,
    create_tod_consumption_plot,
    create_tod_consumption_plot_interactive,
    create_mean_trend_vs_irregularities_plot,
    create_monthly_settled_heatmap,
    create_monthly_settled_heatmap_interactive,
    create_tod_line_chart,
    create_tod_line_chart_interactive
)
from db.db_setup import CONN
from frontend.ui_components.dashboard_controls import get_interactive_plot_setting

from helper.setup_logger import setup_logger



logging = setup_logger("tod_display", "tod_display.log")



def display_monthly_tod_before_banking(selected_plant):
    """Display monthly ToD before banking chart with comprehensive error handling."""
    try:
        logging.info(f"Starting monthly ToD before banking display for plant: {selected_plant}")
        
        # Validate input parameters
        if not selected_plant:
            logging.warning("No plant selected for monthly ToD before banking display")
            st.warning("⚠️ Please select a plant to view the monthly ToD data.")
            return
        
        # Get universal plot options setting
        use_interactive = get_interactive_plot_setting()
        logging.info(f"Using interactive plots: {use_interactive}")
        
        # Fetch data with error handling
        try:
            df = fetch_all_daily_tod_data(CONN, selected_plant)
            if df is None or df.empty:
                logging.warning(f"No monthly ToD data available for plant: {selected_plant}")
                st.warning("📊 No monthly ToD data available for the selected plant and period.")
                return
            
            logging.info(f"Successfully fetched {len(df)} records for monthly ToD before banking - {selected_plant}")
            
        except Exception as e:
            logging.error(f"Database error fetching monthly ToD data for {selected_plant}: {str(e)}")
            st.error("❌ Unable to retrieve data from the database. Please try again or contact support.")
            return
        
        # Generate and display plot
        try:
            if use_interactive:
                logging.info(f"Creating interactive monthly ToD before banking plot for {selected_plant}")
                fig = create_monthly_before_banking_plot_interactive(df, selected_plant)
                if fig:
                    st.plotly_chart(fig, use_container_width=True)
                    logging.info(f"Successfully displayed interactive monthly ToD before banking plot for {selected_plant}")
                else:
                    logging.error(f"Failed to generate interactive monthly ToD before banking plot for {selected_plant}")
                    st.warning("⚠️ Unable to generate the interactive chart. Please try switching to standard view or contact support.")
            else:
                logging.info(f"Creating standard monthly ToD before banking plot for {selected_plant}")
                fig = create_monthly_before_banking_plot(df, selected_plant)
                if fig:
                    st.pyplot(fig)
                    logging.info(f"Successfully displayed standard monthly ToD before banking plot for {selected_plant}")
                else:
                    logging.error(f"Failed to generate standard monthly ToD before banking plot for {selected_plant}")
                    st.warning("⚠️ Unable to generate the chart. Please try again or contact support.")
                    
        except Exception as e:
            logging.error(f"Error creating monthly ToD before banking plot for {selected_plant}: {str(e)}")
            st.error("❌ Chart generation failed. Please try again or contact support if the issue persists.")
            
    except Exception as e:
        logging.error(f"Unexpected error in display_monthly_tod_before_banking for {selected_plant}: {str(e)}")
        st.error("❌ An unexpected error occurred. Please refresh the page or contact support.")


def display_monthly_banking_settlement(selected_plant):
    """Display monthly banking settlement chart with comprehensive error handling."""
    try:
        logging.info(f"Starting monthly banking settlement display for plant: {selected_plant}")
        
        # Validate input parameters
        if not selected_plant:
            logging.warning("No plant selected for monthly banking settlement display")
            st.warning("⚠️ Please select a plant to view the banking settlement data.")
            return
        
        # Fetch data with error handling
        try:
            df = fetch_combined_monthly_data(CONN, selected_plant)
            if df is None or df.empty:
                logging.warning(f"No monthly banking settlement data available for plant: {selected_plant}")
                st.warning("📊 No monthly banking settlement data available for the selected plant.")
                return
            
            logging.info(f"Successfully fetched {len(df)} records for monthly banking settlement - {selected_plant}")
            
        except Exception as e:
            logging.error(f"Database error fetching monthly banking settlement data for {selected_plant}: {str(e)}")
            st.error("❌ Unable to retrieve banking settlement data from the database. Please try again or contact support.")
            return

        # Generate and display plot
        try:
            use_interactive = get_interactive_plot_setting()
            logging.info(f"Using interactive plots: {use_interactive}")
            
            if use_interactive:
                logging.info(f"Creating interactive monthly banking settlement chart for {selected_plant}")
                fig = create_monthly_banking_settlement_chart_interactive(df, selected_plant)
                if fig:
                    st.plotly_chart(fig, use_container_width=True)
                    logging.info(f"Successfully displayed interactive monthly banking settlement chart for {selected_plant}")
                else:
                    logging.error(f"Failed to generate interactive monthly banking settlement chart for {selected_plant}")
                    st.warning("⚠️ Unable to generate the interactive banking settlement chart. Please try switching to standard view or contact support.")
            else:
                logging.info(f"Creating standard monthly banking settlement chart for {selected_plant}")
                fig, summary_df = create_monthly_banking_settlement_chart(df, selected_plant)
                if fig:
                    st.pyplot(fig)
                    logging.info(f"Successfully displayed standard monthly banking settlement chart for {selected_plant}")
                else:
                    logging.error(f"Failed to generate standard monthly banking settlement chart for {selected_plant}")
                    st.warning("⚠️ Unable to generate the banking settlement chart. Please try again or contact support.")
                    
        except Exception as e:
            logging.error(f"Error creating monthly banking settlement chart for {selected_plant}: {str(e)}")
            st.error("❌ Chart generation failed. Please try again or contact support if the issue persists.")
            return

        # Calculate and display metrics
        try:
            logging.info(f"Processing metrics for monthly banking settlement - {selected_plant}")
            
            if not df.empty:
                # Calculate additional metrics for banking settlement
                summary_df = df.copy()
                
                # Validate required columns for calculations
                required_columns = ['total_intra_settlement', 'total_inter_settlement', 'total_matched_settled_sum', 'surplus_demand_sum_after_inter']
                missing_columns = [col for col in required_columns if col not in summary_df.columns]
                
                if missing_columns:
                    logging.warning(f"Missing columns for banking settlement metrics calculation: {missing_columns}")
                    st.warning("⚠️ Some banking settlement metrics may not be available due to data structure changes.")
                
                # Calculate derived metrics with error handling
                try:
                    summary_df['settlement_with_banking'] = summary_df['total_intra_settlement'] + summary_df['total_inter_settlement']
                    summary_df['total_settlement'] = summary_df['settlement_with_banking'] + summary_df['total_matched_settled_sum']
                    summary_df['surplus_demand_after_banking'] = summary_df['surplus_demand_sum_after_inter'].clip(lower=0)
                    
                    logging.info(f"Successfully calculated derived metrics for {selected_plant}")
                    
                except Exception as e:
                    logging.error(f"Error calculating derived metrics for {selected_plant}: {str(e)}")
                    st.warning("⚠️ Unable to calculate some banking settlement metrics. Basic data will be displayed.")
                
                # Calculate totals for metric boxes with error handling
                try:
                    total_generation_mwh = summary_df['total_generation_sum'].sum() / 1000 if 'total_generation_sum' in summary_df.columns else 0
                    loss_percentage = 2.8  
                    total_generation_after_loss_mwh = total_generation_mwh * (1 - loss_percentage/100)
                    total_consumption_mwh = summary_df['total_consumption_sum'].sum() / 1000 if 'total_consumption_sum' in summary_df.columns else 0
                    total_settlement_mwh = summary_df['total_settlement'].sum() / 1000 if 'total_settlement' in summary_df.columns else 0
                    total_surplus_demand_after_banking_mwh = summary_df['surplus_demand_after_banking'].sum() / 1000 if 'surplus_demand_after_banking' in summary_df.columns else 0
                    
                    # Calculate replacement percentage with banking
                    replacement_percentage_with_banking = (
                        (total_settlement_mwh / total_consumption_mwh) * 100 
                        if total_consumption_mwh > 0 else 0
                    )
                    
                    logging.info(f"Successfully calculated totals for metric boxes - {selected_plant}")
                    
                except Exception as e:
                    logging.error(f"Error calculating totals for metric boxes for {selected_plant}: {str(e)}")
                    st.warning("⚠️ Unable to calculate summary metrics. Chart data will be displayed without metrics.")
                    return
                
                # Display metrics with error handling
                try:
                    # Add CSS to style metric containers
                    st.markdown("""
                    <style>
                    [data-testid="metric-container"] [data-testid="metric-container-label"] {
                        font-size: 0.9em;
                        font-weight: bold;
                    }
                    [data-testid="metric-container"] [data-testid="metric-container-value"] {
                        font-size: 0.7em;
                    }
                    </style>
                    """, unsafe_allow_html=True)
                    
                    # Create 5 horizontal boxes with main metrics using Streamlit columns
                    col1, col2, col3, col4, col5 = st.columns(5)
                    with col1:
                        st.metric(
                            label="Total Generation (MWh)",
                            value=f"{total_generation_mwh:.2f}"
                        )
                    with col2:
                        st.metric(
                            label="Generation (after loss)",
                            value=f"{total_generation_after_loss_mwh:.2f}",
                            help=f"Generation after {loss_percentage}% transmission/distribution loss"
                        )
                    with col3:
                        st.metric(
                            label="Total Consumption (MWh)",
                            value=f"{total_consumption_mwh:.2f}"
                        )
                    with col4:
                        st.metric(
                            label="Replacement (with Banking) %",
                            value=f"{replacement_percentage_with_banking:.2f}%",
                            help="Percentage of Replacement (with Banking)"
                        )
                    with col5:
                        st.metric(
                            label="Surplus Demand (after Banking)",
                            value=f"{total_surplus_demand_after_banking_mwh:.2f}",
                            help="Remaining demand after considering all banking settlements"
                        )
                    
                    logging.info(f"Successfully displayed metrics for {selected_plant}")
                    
                except Exception as e:
                    logging.error(f"Error displaying metrics for {selected_plant}: {str(e)}")
                    st.warning("⚠️ Unable to display metrics. Chart data will be shown without summary metrics.")
                
                # Display summary dataframe with error handling
                try:
                    # Customize the summary dataframe for display
                    display_df = summary_df.copy()
                    
                    # Select and rename columns for display
                    columns_to_display = {
                        'month': 'Month',
                        'total_matched_settled_sum': 'Settlement (Without Banking)',
                        'settlement_with_banking': 'Settlement (With Banking)',
                        'total_settlement': 'Total Settlement',
                    }
                    
                    # Validate columns exist before filtering
                    available_columns = {k: v for k, v in columns_to_display.items() if k in display_df.columns}
                    
                    if available_columns:
                        # Filter and rename columns
                        display_df = display_df[list(available_columns.keys())].rename(columns=available_columns)
                        
                        st.subheader("📊 Monthly Banking Settlement Summary")
                        st.dataframe(display_df, use_container_width=True)
                        
                        logging.info(f"Successfully displayed summary dataframe for {selected_plant}")
                    else:
                        logging.warning(f"No valid columns available for summary display - {selected_plant}")
                        st.info("📊 Summary data structure has changed. Please contact support for updated view.")
                        
                except Exception as e:
                    logging.error(f"Error displaying summary dataframe for {selected_plant}: {str(e)}")
                    st.warning("⚠️ Unable to display summary table. Chart data is available above.")
                    
            else:
                logging.info(f"No summary data to display for {selected_plant}")
                st.info("📊 No summary data available for the selected period.")
                
        except Exception as e:
            logging.error(f"Error processing metrics for monthly banking settlement {selected_plant}: {str(e)}")
            st.warning("⚠️ Unable to process summary metrics. Chart data is available above.")

    except Exception as e:
        logging.error(f"Unexpected error in display_monthly_banking_settlement for {selected_plant}: {str(e)}")
        st.error("❌ An unexpected error occurred while displaying banking settlement data. Please refresh the page or contact support.")


def display_tod_generation_vs_consumption(selected_plant, start_date, end_date=None):
    """Display ToD generation vs consumption plot with comprehensive error handling."""
    try:
        logging.info(f"Starting ToD generation vs consumption display for plant: {selected_plant}, dates: {start_date} to {end_date}")
        
        # Validate input parameters
        if not selected_plant:
            logging.warning("No plant selected for ToD generation vs consumption display")
            st.warning("⚠️ Please select a plant to view the ToD generation vs consumption data.")
            return
        
        if not start_date:
            logging.warning("No start date provided for ToD generation vs consumption display")
            st.warning("⚠️ Please select a date range to view the ToD generation vs consumption data.")
            return
        
        # Fetch data with error handling
        try:
            df = fetch_tod_binned_data(CONN, selected_plant, start_date, end_date)
            if df is None or df.empty:
                logging.warning(f"No ToD generation vs consumption data available for plant: {selected_plant}, dates: {start_date} to {end_date}")
                st.warning("📊 No ToD generation vs consumption data available for the selected plant and period.")
                return
            
            logging.info(f"Successfully fetched {len(df)} records for ToD generation vs consumption - {selected_plant}")
            
        except Exception as e:
            logging.error(f"Database error fetching ToD generation vs consumption data for {selected_plant}: {str(e)}")
            st.error("❌ Unable to retrieve ToD data from the database. Please try again or contact support.")
            return
        
        # Generate and display plot
        try:
            logging.info(f"Creating ToD generation vs consumption plot for {selected_plant}")
            fig = create_tod_binned_plot(df, selected_plant, start_date, end_date)
            if fig:
                st.pyplot(fig)
                logging.info(f"Successfully displayed ToD generation vs consumption plot for {selected_plant}")
            else:
                logging.error(f"Failed to generate ToD generation vs consumption plot for {selected_plant}")
                st.warning("⚠️ Unable to generate the ToD generation vs consumption chart. Please try again or contact support.")
                
        except Exception as e:
            logging.error(f"Error creating ToD generation vs consumption plot for {selected_plant}: {str(e)}")
            st.error("❌ Chart generation failed. Please try again or contact support if the issue persists.")

    except Exception as e:
        logging.error(f"Unexpected error in display_tod_generation_vs_consumption for {selected_plant}: {str(e)}")
        st.error("❌ An unexpected error occurred. Please refresh the page or contact support.")


def display_tod_generation(selected_plant, start_date, end_date=None):
 
    """Display ToD generation plot with comprehensive error handling."""
    try:
        logging.info(f"Starting ToD generation display for plant: {selected_plant}, dates: {start_date} to {end_date}")
        
        # Validate input parameters
        if not selected_plant:
            logging.warning("No plant selected for ToD generation display")
            st.warning("⚠️ Please select a plant to view the ToD generation data.")
            return
        
        if not start_date:
            logging.warning("No start date provided for ToD generation display")
            st.warning("⚠️ Please select a date range to view the ToD generation data.")
            return
        
        # Get universal plot options setting
        use_interactive = get_interactive_plot_setting()
        logging.info(f"Using interactive plots: {use_interactive}")
        
        # Fetch data with error handling
        try:
        
            df = fetch_daily_tod_data(CONN, selected_plant, start_date, end_date)
         
            if df is None or df.empty:
                logging.warning(f"No ToD generation data available for plant: {selected_plant}, dates: {start_date} to {end_date}")
                st.warning("📊 No ToD generation data available for the selected plant and period.")
                return
            
            logging.info(f"Successfully fetched {len(df)} records for ToD generation - {selected_plant}")
            
        except Exception as e:
            logging.error(f"Database error fetching ToD generation data for {selected_plant}: {str(e)}")
            st.error("❌ Unable to retrieve ToD generation data from the database. Please try again or contact support.")
            return
        


        
        
        # Generate and display plot
        try:
            if use_interactive:
                logging.info(f"Creating interactive ToD generation plot for {selected_plant}")
                fig = create_tod_generation_plot_interactive(df, selected_plant, start_date, end_date)
                if fig:
                    st.plotly_chart(fig, use_container_width=True)
                    logging.info(f"Successfully displayed interactive ToD generation plot for {selected_plant}")
                else:
                    logging.error(f"Failed to generate interactive ToD generation plot for {selected_plant}")
                    st.warning("⚠️ Unable to generate the interactive ToD generation chart. Please try switching to standard view or contact support.")
            else:
                logging.info(f"Creating standard ToD generation plot for {selected_plant}")
                fig = create_tod_generation_plot(df, selected_plant, start_date, end_date)
                if fig:
                    st.pyplot(fig)
                    logging.info(f"Successfully displayed standard ToD generation plot for {selected_plant}")
                else:
                    logging.error(f"Failed to generate standard ToD generation plot for {selected_plant}")
                    st.warning("⚠️ Unable to generate the ToD generation chart. Please try again or contact support.")
                    
        except Exception as e:
            logging.error(f"Error creating ToD generation plot for {selected_plant}: {str(e)}")
            st.error("❌ Chart generation failed. Please try again or contact support if the issue persists.")
        
    except Exception as e:
        logging.error(f"Unexpected error in display_tod_generation for {selected_plant}: {str(e)}")
        st.error("❌ An unexpected error occurred. Please refresh the page or contact support.")


def display_tod_consumption(selected_plant, start_date, end_date=None):
    """Display ToD consumption plot with comprehensive error handling."""
    try:
        logging.info(f"Starting ToD consumption display for plant: {selected_plant}, dates: {start_date} to {end_date}")
        
        # Validate input parameters
        if not selected_plant:
            logging.warning("No plant selected for ToD consumption display")
            st.warning("⚠️ Please select a plant to view the ToD consumption data.")
            return
        
        if not start_date:
            logging.warning("No start date provided for ToD consumption display")
            st.warning("⚠️ Please select a date range to view the ToD consumption data.")
            return
        
        # Get universal plot options setting
        use_interactive = get_interactive_plot_setting()
        logging.info(f"Using interactive plots: {use_interactive}")
        
        # Fetch data with error handling
        try:
            df = fetch_daily_tod_data(CONN, selected_plant, start_date, end_date)
            if df is None or df.empty:
                logging.warning(f"No ToD consumption data available for plant: {selected_plant}, dates: {start_date} to {end_date}")
                st.warning("📊 No ToD consumption data available for the selected plant and period.")
                return
            
            logging.info(f"Successfully fetched {len(df)} records for ToD consumption - {selected_plant}")
            
        except Exception as e:
            logging.error(f"Database error fetching ToD consumption data for {selected_plant}: {str(e)}")
            st.error("❌ Unable to retrieve ToD consumption data from the database. Please try again or contact support.")
            return
        
        # Generate and display plot
        try:
            if use_interactive:
                logging.info(f"Creating interactive ToD consumption plot for {selected_plant}")
                fig = create_tod_consumption_plot_interactive(df, selected_plant, start_date, end_date)
                if fig:
                    st.plotly_chart(fig, use_container_width=True)
                    logging.info(f"Successfully displayed interactive ToD consumption plot for {selected_plant}")
                else:
                    logging.error(f"Failed to generate interactive ToD consumption plot for {selected_plant}")
                    st.warning("⚠️ Unable to generate the interactive ToD consumption chart. Please try switching to standard view or contact support.")
            else:
                logging.info(f"Creating standard ToD consumption plot for {selected_plant}")
                fig = create_tod_consumption_plot(df, selected_plant, start_date, end_date)
                if fig:
                    st.pyplot(fig)
                    logging.info(f"Successfully displayed standard ToD consumption plot for {selected_plant}")
                else:
                    logging.error(f"Failed to generate standard ToD consumption plot for {selected_plant}")
                    st.warning("⚠️ Unable to generate the ToD consumption chart. Please try again or contact support.")
                    
        except Exception as e:
            logging.error(f"Error creating ToD consumption plot for {selected_plant}: {str(e)}")
            st.error("❌ Chart generation failed. Please try again or contact support if the issue persists.")

    except Exception as e:
        logging.error(f"Unexpected error in display_tod_consumption for {selected_plant}: {str(e)}")
        st.error("❌ An unexpected error occurred while displaying the ToD consumption data. Please refresh the page and try again.")


def display_tod_line_chart(selected_plant, start_date, end_date):
    """Display ToD line chart showing generation and consumption trends across time slots."""
    try:
        logging.info(f"Starting ToD line chart display for plant: {selected_plant}")

        # Validate input parameters
        if not selected_plant:
            logging.warning("No plant selected for ToD line chart display")
            st.warning("⚠️ Please select a plant to view the ToD line chart.")
            return

        if not start_date or not end_date:
            logging.warning("Missing date parameters for ToD line chart display")
            st.warning("⚠️ Please select both start and end dates to view the ToD line chart.")
            return

        # Get universal plot options setting
        use_interactive = get_interactive_plot_setting()
        logging.info(f"Using interactive plots: {use_interactive}")

        # Fetch data with error handling
        try:
            df = fetch_daily_tod_data(CONN, selected_plant, start_date, end_date)
            if df is None or df.empty:
                logging.warning(f"No ToD line chart data available for plant: {selected_plant}, dates: {start_date} to {end_date}")
                st.warning("📊 No ToD line chart data available for the selected plant and period.")
                return

            logging.info(f"Successfully fetched {len(df)} records for ToD line chart - {selected_plant}")

        except Exception as e:
            logging.error(f"Database error fetching ToD line chart data for {selected_plant}: {str(e)}")
            st.error("❌ Unable to retrieve ToD line chart data from the database. Please try again or contact support.")
            return

        # Generate and display plot
        try:
            if use_interactive:
                logging.info(f"Creating interactive ToD line chart for {selected_plant}")
                fig = create_tod_line_chart_interactive(df, selected_plant, start_date, end_date)
                if fig:
                    st.plotly_chart(fig, use_container_width=True)
                    logging.info(f"Successfully displayed interactive ToD line chart for {selected_plant}")
                else:
                    logging.error(f"Failed to generate interactive ToD line chart for {selected_plant}")
                    st.warning("⚠️ Unable to generate the interactive ToD line chart. Please try switching to standard view or contact support.")
            else:
                logging.info(f"Creating standard ToD line chart for {selected_plant}")
                fig = create_tod_line_chart(df, selected_plant, start_date, end_date)
                if fig:
                    st.pyplot(fig)
                    logging.info(f"Successfully displayed standard ToD line chart for {selected_plant}")
                else:
                    logging.error(f"Failed to generate standard ToD line chart for {selected_plant}")
                    st.warning("⚠️ Unable to generate the ToD line chart. Please try again or contact support.")

        except Exception as e:
            logging.error(f"Error creating ToD line chart for {selected_plant}: {str(e)}")
            st.error("❌ Chart generation failed. Please try again or contact support if the issue persists.")

    except Exception as e:
        logging.error(f"Unexpected error in display_tod_line_chart for {selected_plant}: {str(e)}")
        st.error("❌ An unexpected error occurred while displaying the ToD line chart. Please try again or contact support.")

    except Exception as e:
        logging.error(f"Unexpected error in display_tod_consumption for {selected_plant}: {str(e)}")
        st.error("❌ An unexpected error occurred. Please refresh the page or contact support.")


def display_mean_trend_vs_irregularities(selected_plant, start_date, end_date):
    """Display mean trend vs irregularities plot with comprehensive error handling."""
    try:
        logging.info(f"Starting mean trend vs irregularities display for plant: {selected_plant}")
        
        # Validate input parameters
        if not selected_plant:
            logging.warning("No plant selected for mean trend vs irregularities display")
            st.warning("⚠️ Please select a plant to view the mean trend vs irregularities analysis.")
            return
            
        if not start_date or not end_date:
            logging.warning("No date range selected for mean trend vs irregularities display")
            st.warning("⚠️ Please select a date range to view the mean trend vs irregularities analysis.")
            return
        
        # Fetch hourly generation data with error handling
        try:
            df = fetch_hourly_generation_data(CONN, selected_plant, start_date, end_date)
            if df is None or df.empty:
                logging.warning(f"No hourly generation data available for plant: {selected_plant}")
                st.warning("📊 No hourly generation data available for the selected plant and date range.")
                return
            
            logging.info(f"Successfully fetched {len(df)} hourly records for mean trend analysis - {selected_plant}")
            
        except Exception as e:
            logging.error(f"Database error fetching hourly generation data for {selected_plant}: {str(e)}")
            st.error("❌ Unable to retrieve hourly generation data from the database. Please try again or contact support.")
            return
        
        # Generate and display plot
        try:
            logging.info(f"Creating mean trend vs irregularities plot for {selected_plant}")
            fig = create_mean_trend_vs_irregularities_plot(df, selected_plant)
            if fig:
                st.plotly_chart(fig, use_container_width=True)
                logging.info(f"Successfully displayed mean trend vs irregularities plot for {selected_plant}")
                
                # Display summary statistics
                try:
                    # Calculate summary statistics
                    total_data_points = len(df)
                    unique_hours = df['time'].nunique()
                    date_range_days = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days + 1
                    
                    # Calculate irregularities
                    trend_stats = df.groupby('time')['generation_kwh'].agg(['mean', 'std']).reset_index()
                    trend_stats.columns = ['time', 'mean_gen', 'std_gen']
                    
                    df_with_stats = pd.merge(df, trend_stats, on='time', how='left')
                    df_with_stats['upper_threshold'] = df_with_stats['mean_gen'] + 2 * df_with_stats['std_gen']
                    df_with_stats['lower_threshold'] = df_with_stats['mean_gen'] - 2 * df_with_stats['std_gen']
                    df_with_stats['is_irregular'] = (
                        (df_with_stats['generation_kwh'] > df_with_stats['upper_threshold']) |
                        (df_with_stats['generation_kwh'] < df_with_stats['lower_threshold'])
                    )
                    
                    total_irregularities = df_with_stats['is_irregular'].sum()
                    irregularity_percentage = (total_irregularities / total_data_points) * 100 if total_data_points > 0 else 0
                    
                    # Display metrics
                    st.markdown("### 📊 Analysis Summary")
                    col1, col2, col3, col4 = st.columns(4)
                    
                    with col1:
                        st.metric(
                            label="Total Data Points",
                            value=f"{total_data_points:,}"
                        )
                    
                    with col2:
                        st.metric(
                            label="Time Periods",
                            value=f"{unique_hours} hours"
                        )
                    
                    with col3:
                        st.metric(
                            label="Date Range",
                            value=f"{date_range_days} days"
                        )
                    
                    with col4:
                        st.metric(
                            label="Irregularities",
                            value=f"{total_irregularities} ({irregularity_percentage:.1f}%)"
                        )
                    
                    logging.info(f"Successfully displayed summary statistics for {selected_plant}")
                    
                except Exception as e:
                    logging.error(f"Error calculating summary statistics for {selected_plant}: {str(e)}")
                    st.warning("⚠️ Unable to calculate summary statistics.")
                
            else:
                logging.error(f"Failed to generate mean trend vs irregularities plot for {selected_plant}")
                st.warning("⚠️ Unable to generate the mean trend vs irregularities chart. Please try again or contact support.")
                
        except Exception as e:
            logging.error(f"Error creating mean trend vs irregularities plot for {selected_plant}: {str(e)}")
            st.error("❌ Chart generation failed. Please try again or contact support if the issue persists.")
            
    except Exception as e:
        logging.error(f"Unexpected error in display_mean_trend_vs_irregularities for {selected_plant}: {str(e)}")
        st.error("❌ An unexpected error occurred. Please refresh the page or contact support.")


def display_monthly_settled_heatmap(selected_plant):
    """Display monthly ToD settled values heatmap with comprehensive error handling."""
    try:
        logging.info(f"Starting monthly settled heatmap display for plant: {selected_plant}")
        
        # Validate input parameters
        if not selected_plant:
            logging.warning("No plant selected for monthly settled heatmap display")
            st.warning("⚠️ Please select a plant to view the settled values heatmap.")
            return
        
        # Get universal plot options setting
        use_interactive = get_interactive_plot_setting()
        logging.info(f"Using interactive plots: {use_interactive}")
        
        # Fetch data with error handling
        try:
            df = fetch_all_daily_tod_data(CONN, selected_plant)
            if df is None or df.empty:
                logging.warning(f"No monthly settled heatmap data available for plant: {selected_plant}")
                st.warning("📊 No settled values data available for the selected plant and period.")
                return
            
            # Check if 'settled' column exists
            if 'settled' not in df.columns:
                logging.warning(f"'settled' column not found in data for plant: {selected_plant}")
                st.warning("📊 Settled values data not available for the selected plant. This chart requires settlement data.")
                return
            
            logging.info(f"Successfully fetched {len(df)} records for monthly settled heatmap - {selected_plant}")
            
        except Exception as e:
            logging.error(f"Database error fetching monthly settled heatmap data for {selected_plant}: {str(e)}")
            st.error("❌ Unable to retrieve data from the database. Please try again or contact support.")
            return
        
        # Generate and display plot
        try:
            if use_interactive:
                logging.info(f"Creating interactive monthly settled heatmap for {selected_plant}")
                fig = create_monthly_settled_heatmap_interactive(df, selected_plant)
                if fig:
                    st.plotly_chart(fig, use_container_width=True)
                    logging.info(f"Successfully displayed interactive monthly settled heatmap for {selected_plant}")
                else:
                    logging.error(f"Failed to generate interactive monthly settled heatmap for {selected_plant}")
                    st.warning("⚠️ Unable to generate the interactive heatmap. Please try switching to standard view or contact support.")
            else:
                logging.info(f"Creating standard monthly settled heatmap for {selected_plant}")
                fig = create_monthly_settled_heatmap(df, selected_plant)
                if fig:
                    st.pyplot(fig)
                    logging.info(f"Successfully displayed standard monthly settled heatmap for {selected_plant}")
                else:
                    logging.error(f"Failed to generate standard monthly settled heatmap for {selected_plant}")
                    st.warning("⚠️ Unable to generate the heatmap. Please try again or contact support.")
                    
        except Exception as e:
            logging.error(f"Error creating monthly settled heatmap for {selected_plant}: {str(e)}")
            st.error("❌ Chart generation failed. Please try again or contact support if the issue persists.")
            
    except Exception as e:
        logging.error(f"Unexpected error in display_monthly_settled_heatmap for {selected_plant}: {str(e)}")
        st.error("❌ An unexpected error occurred. Please refresh the page or contact support.")
