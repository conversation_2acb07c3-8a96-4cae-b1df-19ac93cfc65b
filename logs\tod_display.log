2025-07-08 12:02:37,435 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 12:02:37,435 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 12:02:38,166 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 12:02:38,166 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:02:38,356 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:02:38,356 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 12:02:39,586 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:02:39,586 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 12:02:39,586 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:02:39,716 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:02:39,716 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:02:39,716 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 12:02:39,723 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 12:02:39,725 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 12:02:39,735 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 12:02:39,738 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:39,897 - WARNING - tod_display - tod_display.py:315 - No ToD generation vs consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:39,897 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:39,897 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 12:02:40,064 - WARNING - tod_display - tod_display.py:370 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:40,064 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:40,064 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 12:02:40,231 - WARNING - tod_display - tod_display.py:435 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:40,231 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 12:02:40,382 - WARNING - tod_display - tod_display.py:496 - No hourly generation data available for plant: Kids Clinic India Limited
2025-07-08 12:02:49,069 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 12:02:49,069 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 12:02:49,819 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 12:02:49,819 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:02:49,938 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:02:49,941 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 12:02:51,122 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:02:51,122 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 12:02:51,123 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:02:51,139 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:02:51,139 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:02:51,147 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 12:02:51,147 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 12:02:51,149 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 12:02:51,156 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 12:02:51,156 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:02:51,656 - INFO - tod_display - tod_display.py:319 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 12:02:51,656 - INFO - tod_display - tod_display.py:328 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:02:52,031 - INFO - tod_display - tod_display.py:332 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:02:52,048 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:02:52,048 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 12:02:52,561 - INFO - tod_display - tod_display.py:374 - Successfully fetched 120 records for ToD generation - Kids Clinic India Limited
2025-07-08 12:02:52,561 - INFO - tod_display - tod_display.py:384 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:02:52,586 - INFO - tod_display - tod_display.py:388 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:02:52,586 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:02:52,586 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 12:02:53,074 - INFO - tod_display - tod_display.py:439 - Successfully fetched 120 records for ToD consumption - Kids Clinic India Limited
2025-07-08 12:02:53,098 - INFO - tod_display - tod_display.py:449 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:02:53,128 - INFO - tod_display - tod_display.py:453 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:02:53,133 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 12:02:53,712 - INFO - tod_display - tod_display.py:500 - Successfully fetched 720 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 12:02:53,712 - INFO - tod_display - tod_display.py:509 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:02:53,750 - INFO - tod_display - tod_display.py:513 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:02:53,750 - INFO - tod_display - tod_display.py:565 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 12:28:14,657 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking area chart display for plant: Kids Clinic India Limited
2025-07-08 12:28:14,657 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 12:28:14,930 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 12:28:14,931 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking area chart for Kids Clinic India Limited
2025-07-08 12:28:15,024 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking area chart for Kids Clinic India Limited
2025-07-08 12:28:15,028 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 12:28:16,261 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:28:16,262 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 12:28:16,262 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:28:16,309 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:28:16,309 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:28:16,313 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 12:28:16,315 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 12:28:16,315 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 12:28:16,325 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 12:28:16,331 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:16,512 - INFO - tod_display - tod_display.py:319 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 12:28:16,512 - INFO - tod_display - tod_display.py:328 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:28:17,090 - INFO - tod_display - tod_display.py:332 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:28:17,107 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:17,107 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 12:28:17,310 - INFO - tod_display - tod_display.py:374 - Successfully fetched 120 records for ToD generation - Kids Clinic India Limited
2025-07-08 12:28:17,310 - INFO - tod_display - tod_display.py:384 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:28:17,328 - INFO - tod_display - tod_display.py:388 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:28:17,341 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:17,341 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 12:28:17,555 - INFO - tod_display - tod_display.py:439 - Successfully fetched 120 records for ToD consumption - Kids Clinic India Limited
2025-07-08 12:28:17,555 - INFO - tod_display - tod_display.py:449 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:28:17,595 - INFO - tod_display - tod_display.py:453 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:28:17,599 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 12:28:18,060 - INFO - tod_display - tod_display.py:500 - Successfully fetched 720 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 12:28:18,060 - INFO - tod_display - tod_display.py:509 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:28:18,108 - INFO - tod_display - tod_display.py:513 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:28:18,132 - INFO - tod_display - tod_display.py:565 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 12:28:27,395 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 12:28:27,396 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 12:28:27,709 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 12:28:27,710 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:28:27,757 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 12:28:27,761 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 12:28:28,774 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:28:28,774 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 12:28:28,775 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:28:28,795 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 12:28:28,797 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 12:28:28,799 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 12:28:28,800 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 12:28:28,803 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 12:28:28,806 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 12:28:28,809 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:29,002 - INFO - tod_display - tod_display.py:319 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 12:28:29,002 - INFO - tod_display - tod_display.py:328 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:28:29,301 - INFO - tod_display - tod_display.py:332 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 12:28:29,304 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:29,304 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 12:28:29,498 - INFO - tod_display - tod_display.py:374 - Successfully fetched 120 records for ToD generation - Kids Clinic India Limited
2025-07-08 12:28:29,500 - INFO - tod_display - tod_display.py:384 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:28:29,517 - INFO - tod_display - tod_display.py:388 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 12:28:29,520 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:29,520 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 12:28:29,697 - INFO - tod_display - tod_display.py:439 - Successfully fetched 120 records for ToD consumption - Kids Clinic India Limited
2025-07-08 12:28:29,698 - INFO - tod_display - tod_display.py:449 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:28:29,715 - INFO - tod_display - tod_display.py:453 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 12:28:29,717 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 12:28:30,081 - INFO - tod_display - tod_display.py:500 - Successfully fetched 720 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 12:28:30,082 - INFO - tod_display - tod_display.py:509 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:28:30,104 - INFO - tod_display - tod_display.py:513 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 12:28:30,112 - INFO - tod_display - tod_display.py:565 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 14:54:06,383 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 14:54:06,389 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 14:54:06,679 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 14:54:06,680 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 14:54:06,775 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 14:54:06,795 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 14:54:08,446 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 14:54:08,446 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 14:54:08,446 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 14:54:08,497 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 14:54:08,497 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 14:54:08,497 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 14:54:08,501 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 14:54:08,503 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 14:54:08,507 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 14:54:08,512 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,575 - WARNING - tod_display - tod_display.py:315 - No ToD generation vs consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,579 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,580 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 14:54:08,649 - WARNING - tod_display - tod_display.py:370 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,652 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,652 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 14:54:08,735 - WARNING - tod_display - tod_display.py:435 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:08,739 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 14:54:08,820 - WARNING - tod_display - tod_display.py:496 - No hourly generation data available for plant: Kids Clinic India Limited
2025-07-08 14:54:12,182 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 14:54:12,183 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 14:54:12,528 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 14:54:12,528 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 14:54:13,812 - INFO - tod_display - tod_display.py:36 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 14:54:13,812 - INFO - tod_display - tod_display.py:46 - Using interactive plots: True
2025-07-08 14:54:14,118 - INFO - tod_display - tod_display.py:56 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 14:54:14,118 - INFO - tod_display - tod_display.py:66 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 14:54:14,221 - INFO - tod_display - tod_display.py:70 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 14:54:14,221 - INFO - tod_display - tod_display.py:96 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 14:54:15,874 - INFO - tod_display - tod_display.py:112 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 14:54:15,874 - INFO - tod_display - tod_display.py:122 - Using interactive plots: True
2025-07-08 14:54:15,874 - INFO - tod_display - tod_display.py:125 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 14:54:15,902 - INFO - tod_display - tod_display.py:129 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 14:54:15,902 - INFO - tod_display - tod_display.py:150 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 14:54:15,902 - INFO - tod_display - tod_display.py:170 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 14:54:15,902 - INFO - tod_display - tod_display.py:191 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 14:54:15,913 - INFO - tod_display - tod_display.py:244 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 14:54:15,920 - INFO - tod_display - tod_display.py:273 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 14:54:15,925 - INFO - tod_display - tod_display.py:298 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 14:54:16,103 - INFO - tod_display - tod_display.py:319 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 14:54:16,103 - INFO - tod_display - tod_display.py:328 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 14:54:16,647 - INFO - tod_display - tod_display.py:332 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 14:54:16,647 - INFO - tod_display - tod_display.py:349 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 14:54:16,647 - INFO - tod_display - tod_display.py:364 - Using interactive plots: True
2025-07-08 14:54:16,828 - INFO - tod_display - tod_display.py:374 - Successfully fetched 120 records for ToD generation - Kids Clinic India Limited
2025-07-08 14:54:16,828 - INFO - tod_display - tod_display.py:384 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 14:54:16,879 - INFO - tod_display - tod_display.py:388 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 14:54:16,879 - INFO - tod_display - tod_display.py:414 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 14:54:16,879 - INFO - tod_display - tod_display.py:429 - Using interactive plots: True
2025-07-08 14:54:17,065 - INFO - tod_display - tod_display.py:439 - Successfully fetched 120 records for ToD consumption - Kids Clinic India Limited
2025-07-08 14:54:17,066 - INFO - tod_display - tod_display.py:449 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 14:54:17,095 - INFO - tod_display - tod_display.py:453 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 14:54:17,104 - INFO - tod_display - tod_display.py:479 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 14:54:17,591 - INFO - tod_display - tod_display.py:500 - Successfully fetched 720 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 14:54:17,592 - INFO - tod_display - tod_display.py:509 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 14:54:17,630 - INFO - tod_display - tod_display.py:513 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 14:54:17,665 - INFO - tod_display - tod_display.py:565 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 15:48:10,771 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:48:12,371 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:48:12,371 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 15:48:12,371 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:48:12,620 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:48:12,620 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:48:12,637 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 15:48:12,637 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 15:48:12,637 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 15:48:12,655 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 15:48:12,659 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:12,659 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 15:48:12,728 - WARNING - tod_display - tod_display.py:372 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:12,728 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:12,728 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 15:48:12,810 - WARNING - tod_display - tod_display.py:437 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:12,924 - INFO - tod_display - tod_display.py:38 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 15:48:12,925 - INFO - tod_display - tod_display.py:48 - Using interactive plots: True
2025-07-08 15:48:13,260 - INFO - tod_display - tod_display.py:58 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 15:48:13,260 - INFO - tod_display - tod_display.py:68 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 15:48:13,454 - INFO - tod_display - tod_display.py:72 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 15:48:13,454 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:13,523 - WARNING - tod_display - tod_display.py:317 - No ToD generation vs consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:13,526 - INFO - tod_display - tod_display.py:481 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 15:48:13,585 - WARNING - tod_display - tod_display.py:498 - No hourly generation data available for plant: Kids Clinic India Limited
2025-07-08 15:48:13,585 - INFO - tod_display - tod_display.py:589 - Starting monthly settled heatmap display for plant: Kids Clinic India Limited
2025-07-08 15:48:13,585 - INFO - tod_display - tod_display.py:599 - Using interactive plots: True
2025-07-08 15:48:13,988 - WARNING - tod_display - tod_display.py:611 - 'settled' column not found in data for plant: Kids Clinic India Limited
2025-07-08 15:48:14,386 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:48:15,553 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:48:16,274 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:48:17,754 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:48:17,754 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 15:48:17,754 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:48:17,804 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:48:17,804 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:48:17,804 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 15:48:17,804 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 15:48:17,820 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 15:48:17,826 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 15:48:17,828 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:48:17,830 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 15:48:18,018 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 15:48:18,018 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:48:18,085 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:48:18,088 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:48:18,088 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 15:48:18,253 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 15:48:18,253 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:48:18,304 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:48:19,125 - INFO - tod_display - tod_display.py:38 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 15:48:19,127 - INFO - tod_display - tod_display.py:48 - Using interactive plots: True
2025-07-08 15:48:19,443 - INFO - tod_display - tod_display.py:58 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 15:48:19,444 - INFO - tod_display - tod_display.py:68 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 15:48:19,520 - INFO - tod_display - tod_display.py:72 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 15:48:19,537 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:48:19,703 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 15:48:19,703 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:48:20,207 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:48:20,211 - INFO - tod_display - tod_display.py:481 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 15:48:20,687 - INFO - tod_display - tod_display.py:502 - Successfully fetched 744 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 15:48:20,687 - INFO - tod_display - tod_display.py:511 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 15:48:20,739 - INFO - tod_display - tod_display.py:515 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 15:48:20,759 - INFO - tod_display - tod_display.py:567 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 15:48:20,759 - INFO - tod_display - tod_display.py:589 - Starting monthly settled heatmap display for plant: Kids Clinic India Limited
2025-07-08 15:48:20,759 - INFO - tod_display - tod_display.py:599 - Using interactive plots: True
2025-07-08 15:48:21,038 - WARNING - tod_display - tod_display.py:611 - 'settled' column not found in data for plant: Kids Clinic India Limited
2025-07-08 15:50:57,623 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:50:58,683 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:50:58,685 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 15:50:58,685 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:50:58,704 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:50:58,704 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:50:58,707 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 15:50:58,708 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 15:50:58,709 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 15:50:58,714 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 15:50:58,718 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:50:58,718 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 15:50:58,882 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 15:50:58,897 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:50:58,917 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:50:58,919 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:50:58,920 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 15:50:59,109 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 15:50:59,109 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:50:59,124 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:50:59,421 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:50:59,645 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 15:50:59,645 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:50:59,973 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:50:59,984 - INFO - tod_display - tod_display.py:481 - Starting mean trend vs irregularities display for plant: Kids Clinic India Limited
2025-07-08 15:51:00,382 - INFO - tod_display - tod_display.py:502 - Successfully fetched 744 hourly records for mean trend analysis - Kids Clinic India Limited
2025-07-08 15:51:00,382 - INFO - tod_display - tod_display.py:511 - Creating mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 15:51:00,411 - INFO - tod_display - tod_display.py:515 - Successfully displayed mean trend vs irregularities plot for Kids Clinic India Limited
2025-07-08 15:51:00,424 - INFO - tod_display - tod_display.py:567 - Successfully displayed summary statistics for Kids Clinic India Limited
2025-07-08 15:51:00,429 - INFO - tod_display - tod_display.py:589 - Starting monthly settled heatmap display for plant: Kids Clinic India Limited
2025-07-08 15:51:00,429 - INFO - tod_display - tod_display.py:599 - Using interactive plots: True
2025-07-08 15:51:00,803 - WARNING - tod_display - tod_display.py:611 - 'settled' column not found in data for plant: Kids Clinic India Limited
2025-07-08 15:51:16,000 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:51:17,067 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:51:17,067 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 15:51:17,067 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:51:17,093 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:51:17,093 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:51:17,096 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 15:51:17,096 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 15:51:17,098 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 15:51:17,104 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 15:51:17,107 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:51:17,108 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 15:51:17,284 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 15:51:17,284 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:51:17,306 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:51:17,315 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:51:17,315 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 15:51:17,485 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 15:51:17,485 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:51:17,504 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:51:17,788 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:51:17,956 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 15:51:17,956 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:51:18,223 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:53:06,910 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 15:53:07,876 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:53:07,876 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 15:53:07,876 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:53:07,896 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 15:53:07,896 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 15:53:07,902 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 15:53:07,902 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 15:53:07,904 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 15:53:07,908 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 15:53:07,912 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:53:07,913 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 15:53:08,112 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 15:53:08,112 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:53:08,131 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 15:53:08,134 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:53:08,135 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 15:53:08,345 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 15:53:08,345 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:53:08,379 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 15:53:08,697 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:53:08,906 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 15:53:08,907 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 15:53:09,195 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:03:09,559 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:03:10,520 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:03:10,520 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:03:10,520 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:03:10,545 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:03:10,545 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:03:10,547 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:03:10,547 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:03:10,551 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:03:10,557 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:03:10,561 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:03:10,561 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:03:10,824 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:03:10,824 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:03:10,849 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:03:10,877 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:03:10,877 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:03:11,108 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:03:11,108 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:03:11,129 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:03:11,426 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:03:11,667 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:03:11,668 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:03:11,941 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:04:51,790 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:04:52,847 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:04:52,848 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:04:52,848 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:04:52,867 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:04:52,868 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:04:52,869 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:04:52,870 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:04:52,870 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:04:52,876 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:04:52,879 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:04:52,879 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:04:53,175 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:04:53,176 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:04:53,198 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:04:53,202 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:04:53,202 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:04:53,503 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:04:53,503 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:04:53,516 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:04:53,813 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:04:53,994 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:04:53,994 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:04:54,286 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:05:24,604 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:05:24,859 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:05:24,859 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:05:25,194 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:05:48,715 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:05:49,674 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:05:49,675 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:05:49,675 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:05:49,695 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:05:49,696 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:05:49,698 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:05:49,698 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:05:49,701 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:05:49,704 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:05:49,707 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:05:49,708 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:05:49,938 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:05:49,938 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:05:49,957 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:05:49,960 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:05:49,960 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:05:50,215 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:05:50,216 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:05:50,235 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:05:50,554 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:05:50,817 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:05:50,817 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:05:51,132 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:17,188 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:07:18,223 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:18,223 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:07:18,223 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:18,247 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:18,247 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:18,251 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:07:18,252 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:07:18,254 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:07:18,260 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:07:18,263 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:18,263 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:07:18,492 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:07:18,493 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:18,511 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:18,516 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:18,516 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:07:18,742 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:07:18,743 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:18,765 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:19,095 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:19,363 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:07:19,364 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:19,639 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:48,942 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:07:49,957 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:49,957 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:07:49,957 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:49,975 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:49,975 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:49,991 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:07:49,993 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:07:49,996 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:07:49,999 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:07:50,002 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:50,002 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:07:50,262 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:07:50,262 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:50,278 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:50,288 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:50,288 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:07:50,654 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:07:50,654 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:50,685 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:51,130 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:51,372 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:07:51,372 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:51,653 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:53,031 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:07:54,031 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:54,032 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:07:54,032 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:54,048 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:07:54,048 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:07:54,048 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:07:54,048 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:07:54,048 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:07:54,061 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:07:54,064 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:54,064 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:07:54,291 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:07:54,292 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:54,308 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:07:54,313 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:54,314 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:07:54,530 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:07:54,530 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:54,546 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:07:54,831 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:07:55,071 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:07:55,072 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:07:55,373 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:08:29,333 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:08:30,281 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:08:30,281 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:08:30,281 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:08:30,307 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:08:30,310 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:08:30,312 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:08:30,313 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:08:30,316 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:08:30,321 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:08:30,324 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:08:30,326 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:08:30,526 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:08:30,527 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:08:30,544 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:08:30,547 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:08:30,548 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:08:30,791 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:08:30,791 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:08:30,812 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:08:31,067 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:08:31,257 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:08:31,257 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:08:31,532 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:09:42,545 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:09:43,566 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:09:43,582 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:09:43,582 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:09:43,599 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:09:43,600 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:09:43,601 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:09:43,602 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:09:43,605 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:09:43,610 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:09:43,611 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:09:43,611 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:09:43,867 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:09:43,867 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:09:43,886 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:09:43,891 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:09:43,891 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:09:44,119 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:09:44,119 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:09:44,158 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:09:44,494 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:09:44,699 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:09:44,700 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:09:44,976 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:10:37,156 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:10:38,159 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:10:38,160 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:10:38,160 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:10:38,178 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:10:38,180 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:10:38,182 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:10:38,183 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:10:38,187 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:10:38,191 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:10:38,196 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:10:38,196 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:10:38,436 - INFO - tod_display - tod_display.py:376 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 16:10:38,436 - INFO - tod_display - tod_display.py:386 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:10:38,468 - INFO - tod_display - tod_display.py:390 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 16:10:38,471 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:10:38,471 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:10:38,721 - INFO - tod_display - tod_display.py:441 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 16:10:38,722 - INFO - tod_display - tod_display.py:451 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:10:38,739 - INFO - tod_display - tod_display.py:455 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 16:10:39,106 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:10:39,370 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 16:10:39,370 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:10:39,622 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 16:59:44,952 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 16:59:45,957 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:59:45,958 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 16:59:45,958 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:59:45,997 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 16:59:45,997 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 16:59:45,997 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 16:59:45,997 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 16:59:45,997 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 16:59:46,010 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 16:59:46,014 - INFO - tod_display - tod_display.py:351 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 16:59:46,014 - INFO - tod_display - tod_display.py:366 - Using interactive plots: True
2025-07-08 16:59:46,086 - WARNING - tod_display - tod_display.py:372 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 16:59:46,091 - INFO - tod_display - tod_display.py:416 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 16:59:46,091 - INFO - tod_display - tod_display.py:431 - Using interactive plots: True
2025-07-08 16:59:46,149 - WARNING - tod_display - tod_display.py:437 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:00:55,481 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:00:56,517 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:00:56,517 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:00:56,517 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:00:56,536 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:00:56,536 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:00:56,538 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:00:56,539 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:00:56,541 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:00:56,545 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:00:56,546 - INFO - tod_display - tod_display.py:357 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:00:56,546 - INFO - tod_display - tod_display.py:372 - Using interactive plots: True
2025-07-08 17:00:56,643 - WARNING - tod_display - tod_display.py:378 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:00:56,646 - INFO - tod_display - tod_display.py:422 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:00:56,646 - INFO - tod_display - tod_display.py:437 - Using interactive plots: True
2025-07-08 17:00:56,696 - WARNING - tod_display - tod_display.py:443 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:01:19,896 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:01:20,953 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:01:20,953 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:01:20,953 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:01:20,980 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:01:20,981 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:01:20,983 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:01:20,984 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:01:20,986 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:01:20,990 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:01:20,990 - INFO - tod_display - tod_display.py:357 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:01:20,990 - INFO - tod_display - tod_display.py:372 - Using interactive plots: True
2025-07-08 17:01:21,064 - WARNING - tod_display - tod_display.py:378 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:01:21,064 - INFO - tod_display - tod_display.py:426 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:01:21,064 - INFO - tod_display - tod_display.py:441 - Using interactive plots: True
2025-07-08 17:01:21,147 - WARNING - tod_display - tod_display.py:447 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:01:42,001 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:01:43,023 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:01:43,023 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:01:43,023 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:01:43,035 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:01:43,035 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:01:43,051 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:01:43,052 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:01:43,054 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:01:43,058 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:01:43,062 - INFO - tod_display - tod_display.py:357 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:01:43,062 - INFO - tod_display - tod_display.py:372 - Using interactive plots: True
2025-07-08 17:01:43,159 - WARNING - tod_display - tod_display.py:378 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:01:43,162 - INFO - tod_display - tod_display.py:426 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:01:43,163 - INFO - tod_display - tod_display.py:441 - Using interactive plots: True
2025-07-08 17:01:43,234 - WARNING - tod_display - tod_display.py:447 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:02:08,223 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:02:09,194 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:02:09,194 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:02:09,194 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:02:09,207 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:02:09,207 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:02:09,217 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:02:09,217 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:02:09,220 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:02:09,223 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:02:09,226 - INFO - tod_display - tod_display.py:357 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:02:09,227 - INFO - tod_display - tod_display.py:372 - Using interactive plots: True
2025-07-08 17:02:09,301 - WARNING - tod_display - tod_display.py:381 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:02:09,320 - INFO - tod_display - tod_display.py:429 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:02:09,320 - INFO - tod_display - tod_display.py:444 - Using interactive plots: True
2025-07-08 17:02:09,423 - WARNING - tod_display - tod_display.py:450 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:02:32,702 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:02:33,667 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:02:33,667 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:02:33,667 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:02:33,690 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:02:33,691 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:02:33,692 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:02:33,692 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:02:33,692 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:02:33,698 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:02:33,700 - INFO - tod_display - tod_display.py:357 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:02:33,702 - INFO - tod_display - tod_display.py:372 - Using interactive plots: True
2025-07-08 17:02:33,772 - WARNING - tod_display - tod_display.py:387 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:02:33,781 - INFO - tod_display - tod_display.py:435 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:02:33,781 - INFO - tod_display - tod_display.py:450 - Using interactive plots: True
2025-07-08 17:02:33,856 - WARNING - tod_display - tod_display.py:456 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:03:05,923 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:03:07,549 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:03:07,549 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:03:07,549 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:03:07,588 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:03:07,588 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:03:07,588 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:03:07,605 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:03:07,608 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:03:07,608 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:03:07,622 - INFO - tod_display - tod_display.py:357 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:03:07,622 - INFO - tod_display - tod_display.py:372 - Using interactive plots: True
2025-07-08 17:03:07,922 - INFO - tod_display - tod_display.py:391 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:03:07,922 - INFO - tod_display - tod_display.py:405 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:03:07,972 - INFO - tod_display - tod_display.py:409 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:03:07,972 - INFO - tod_display - tod_display.py:435 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:03:07,986 - INFO - tod_display - tod_display.py:450 - Using interactive plots: True
2025-07-08 17:03:08,282 - INFO - tod_display - tod_display.py:460 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:03:08,282 - INFO - tod_display - tod_display.py:470 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:03:08,334 - INFO - tod_display - tod_display.py:474 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:03:18,090 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:03:19,096 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:03:19,098 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:03:19,098 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:03:19,112 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:03:19,112 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:03:19,124 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:03:19,125 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:03:19,127 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:03:19,132 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:03:19,134 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:03:19,134 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:03:19,294 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:03:19,294 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:03:19,312 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:03:19,312 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:03:19,322 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:03:19,494 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:03:19,494 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:03:19,512 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:08:25,050 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:08:25,979 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:08:25,979 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:08:25,979 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:08:26,003 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:08:26,003 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:08:26,009 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:08:26,010 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:08:26,012 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:08:26,016 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:08:26,020 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:08:26,020 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:08:26,286 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:08:26,286 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:08:26,309 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:08:26,313 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:08:26,313 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:08:26,491 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:08:26,491 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:08:26,521 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:08:39,774 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:08:40,730 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:08:40,730 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:08:40,730 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:08:40,752 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:08:40,753 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:08:40,754 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:08:40,755 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:08:40,755 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:08:40,760 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:08:40,763 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:08:40,764 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:08:40,989 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:08:40,989 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:08:41,022 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:08:41,025 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:08:41,026 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:08:41,209 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:08:41,209 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:08:41,232 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:09:43,736 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:09:44,937 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:09:44,937 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:09:44,937 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:09:44,960 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:09:44,960 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:09:44,960 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:09:44,960 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:09:44,960 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:09:44,970 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:09:44,974 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:09:44,974 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:09:45,235 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:09:45,235 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:09:45,257 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:09:45,262 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:09:45,262 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:09:45,493 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:09:45,493 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:09:45,522 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:10:13,320 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:10:14,295 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:10:14,295 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:10:14,295 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:10:14,310 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:10:14,310 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:10:14,310 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:10:14,310 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:10:14,323 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:10:14,355 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:10:14,359 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:10:14,359 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:10:14,598 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:10:14,598 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:10:14,617 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:10:14,619 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:10:14,620 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:10:14,801 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:10:14,801 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:10:14,826 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:10:53,367 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:10:55,045 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:10:55,045 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:10:55,045 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:10:55,125 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:10:55,125 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:10:55,125 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:10:55,125 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:10:55,141 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:10:55,151 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:10:55,156 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:10:55,158 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:10:55,223 - WARNING - tod_display - tod_display.py:375 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:10:55,224 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:10:55,224 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:10:55,302 - WARNING - tod_display - tod_display.py:444 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:13:40,101 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:13:41,110 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:13:41,119 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:13:41,119 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:13:41,139 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:13:41,140 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:13:41,142 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:13:41,143 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:13:41,144 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:13:41,150 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:13:41,152 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:13:41,153 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:13:41,354 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:13:41,354 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:13:41,374 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:13:41,374 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:13:41,374 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:13:41,570 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:13:41,570 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:13:41,592 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:17:20,485 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:17:21,519 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:17:21,519 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:17:21,519 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:17:21,543 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:17:21,543 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:17:21,548 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:17:21,549 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:17:21,552 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:17:21,557 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:17:21,560 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:17:21,560 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:17:21,830 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:17:21,830 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:17:21,850 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:17:21,854 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:17:21,854 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:17:22,080 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:17:22,080 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:17:22,100 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:17:32,484 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:17:33,601 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:17:33,601 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:17:33,601 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:17:33,621 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:17:33,621 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:17:33,637 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:17:33,638 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:17:33,641 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:17:33,641 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:17:33,641 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:17:33,641 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:17:33,877 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:17:33,878 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:17:33,892 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:17:33,899 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:17:33,899 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:17:34,108 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:17:34,109 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:17:34,132 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:18:12,657 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:18:13,607 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:18:13,637 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:18:13,637 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:18:13,659 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:18:13,659 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:18:13,659 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:18:13,666 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:18:13,668 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:18:13,672 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:18:13,675 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:18:13,675 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:18:13,882 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:18:13,883 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:18:13,899 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:18:13,902 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:18:13,903 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:18:14,124 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:18:14,124 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:18:14,146 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:18:27,089 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:18:28,025 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:18:28,025 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:18:28,025 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:18:28,051 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:18:28,051 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:18:28,053 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:18:28,053 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:18:28,056 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:18:28,060 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:18:28,062 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:18:28,063 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:18:28,291 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:18:28,291 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:18:28,310 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:18:28,313 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:18:28,313 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:18:28,608 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:18:28,609 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:18:28,643 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:18:41,166 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:18:42,109 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:18:42,109 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:18:42,109 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:18:42,127 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:18:42,128 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:18:42,130 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:18:42,131 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:18:42,131 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:18:42,137 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:18:42,139 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:18:42,140 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:18:42,355 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:18:42,355 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:18:42,359 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:18:42,378 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:18:42,378 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:18:42,576 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:18:42,576 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:18:42,592 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:19:05,106 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:19:06,093 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:19:06,093 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:19:06,094 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:19:06,121 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:19:06,129 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:19:06,130 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:19:06,140 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:19:06,144 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:19:06,148 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:19:06,159 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:19:06,160 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:19:06,395 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:19:06,395 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:19:06,412 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:19:06,428 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:19:06,429 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:19:06,717 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:19:06,718 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:19:06,733 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:19:16,502 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:19:18,062 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:19:18,062 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:19:18,062 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:19:18,117 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:19:18,122 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:19:18,124 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:19:18,124 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:19:18,127 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:19:18,136 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:19:18,146 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:19:18,146 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:19:18,345 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:19:18,345 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:19:18,395 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:19:18,395 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:19:18,395 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:19:18,595 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:19:18,595 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:19:18,629 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:19:29,144 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:19:30,228 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:19:30,228 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:19:30,228 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:19:30,249 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:19:30,261 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:19:30,264 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:19:30,265 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:19:30,267 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:19:30,270 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:19:30,273 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:19:30,275 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:19:30,445 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:19:30,445 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:19:30,467 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:19:30,474 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:19:30,474 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:19:30,662 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:19:30,662 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:19:30,695 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:20:36,517 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:20:37,467 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:20:37,467 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:20:37,467 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:20:37,490 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:20:37,490 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:20:37,490 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:20:37,490 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:20:37,490 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:20:37,498 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:20:37,502 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:20:37,503 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:20:37,683 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:20:37,683 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:20:37,703 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:20:37,706 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:20:37,706 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:20:37,895 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:20:37,895 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:20:37,914 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:20:49,035 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:20:50,006 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:20:50,006 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:20:50,006 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:20:50,024 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:20:50,034 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:20:50,035 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:20:50,036 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:20:50,039 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:20:50,042 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:20:50,043 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:20:50,043 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:20:50,324 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:20:50,324 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:20:50,340 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:20:50,354 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:20:50,354 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:20:50,575 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:20:50,583 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:20:50,585 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:21:15,990 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:21:16,971 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:21:16,971 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:21:16,971 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:21:16,991 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:21:16,991 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:21:16,999 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:21:17,000 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:21:17,003 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:21:17,005 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:21:17,005 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:21:17,005 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:21:17,221 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:21:17,221 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:21:17,239 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:21:17,241 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:21:17,242 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:21:17,470 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:21:17,470 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:21:17,502 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:22:00,555 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:22:01,529 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:22:01,529 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:22:01,529 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:22:01,547 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:22:01,547 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:22:01,550 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:22:01,551 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:22:01,555 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:22:01,560 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:22:01,563 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:22:01,563 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:22:01,858 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:22:01,859 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:22:01,875 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:22:01,878 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:22:01,878 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:22:02,079 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:22:02,079 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:22:02,095 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:22:39,362 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:22:40,428 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:22:40,428 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:22:40,428 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:22:40,461 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:22:40,461 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:22:40,464 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:22:40,465 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:22:40,467 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:22:40,472 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:22:40,474 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:22:40,475 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:22:40,662 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:22:40,662 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:22:40,690 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:22:40,695 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:22:40,695 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:22:40,876 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:22:40,877 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:22:40,893 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:23:00,698 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:23:01,637 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:23:01,637 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:23:01,637 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:23:01,652 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:23:01,660 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:23:01,661 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:23:01,661 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:23:01,661 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:23:01,668 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:23:01,671 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:23:01,671 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:23:01,922 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:23:01,922 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:23:01,950 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:23:01,955 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:23:01,956 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:23:02,160 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:23:02,160 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:23:02,193 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:28:11,951 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:28:12,945 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:28:12,945 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:28:12,945 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:28:12,970 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:28:12,971 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:28:12,972 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:28:12,974 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:28:12,976 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:28:12,980 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:28:12,983 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:28:12,985 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:28:13,203 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:28:13,203 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:28:13,231 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:28:13,266 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:28:13,266 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:28:13,451 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:28:13,451 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:28:13,473 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:28:14,967 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:28:15,184 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 17:28:15,184 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:28:15,469 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:29:57,244 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:29:58,151 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:29:58,152 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:29:58,152 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:29:58,165 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:29:58,165 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:29:58,174 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:29:58,176 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:29:58,176 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:29:58,182 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:29:58,184 - INFO - tod_display - tod_display.py:38 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 17:29:58,185 - INFO - tod_display - tod_display.py:48 - Using interactive plots: True
2025-07-08 17:29:58,504 - INFO - tod_display - tod_display.py:58 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 17:29:58,504 - INFO - tod_display - tod_display.py:68 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:29:58,563 - INFO - tod_display - tod_display.py:72 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:30:00,102 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:30:00,272 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 17:30:00,272 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:30:00,551 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:30:58,369 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:30:59,393 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:30:59,393 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:30:59,393 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:30:59,416 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:30:59,416 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:30:59,418 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:30:59,418 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:30:59,421 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:30:59,426 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:30:59,429 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:30:59,430 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:30:59,602 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:30:59,602 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:30:59,621 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:30:59,633 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:30:59,633 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:30:59,816 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:30:59,816 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:30:59,839 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:31:01,390 - INFO - tod_display - tod_display.py:38 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 17:31:01,390 - INFO - tod_display - tod_display.py:48 - Using interactive plots: True
2025-07-08 17:31:01,659 - INFO - tod_display - tod_display.py:58 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 17:31:01,659 - INFO - tod_display - tod_display.py:68 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:31:01,702 - INFO - tod_display - tod_display.py:72 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:31:01,710 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:31:01,874 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 17:31:01,874 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:31:02,145 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:41:00,692 - INFO - tod_display - tod_display.py:98 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:41:02,638 - INFO - tod_display - tod_display.py:114 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:41:02,638 - INFO - tod_display - tod_display.py:124 - Using interactive plots: True
2025-07-08 17:41:02,639 - INFO - tod_display - tod_display.py:127 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:41:02,679 - INFO - tod_display - tod_display.py:131 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:41:02,680 - INFO - tod_display - tod_display.py:152 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:41:02,682 - INFO - tod_display - tod_display.py:172 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:41:02,684 - INFO - tod_display - tod_display.py:193 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:41:02,689 - INFO - tod_display - tod_display.py:246 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:41:02,695 - INFO - tod_display - tod_display.py:275 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:41:02,699 - INFO - tod_display - tod_display.py:352 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:02,699 - INFO - tod_display - tod_display.py:367 - Using interactive plots: True
2025-07-08 17:41:02,939 - INFO - tod_display - tod_display.py:379 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:41:02,939 - INFO - tod_display - tod_display.py:393 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:41:02,973 - INFO - tod_display - tod_display.py:397 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:41:02,978 - INFO - tod_display - tod_display.py:423 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:02,978 - INFO - tod_display - tod_display.py:438 - Using interactive plots: True
2025-07-08 17:41:03,202 - INFO - tod_display - tod_display.py:448 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:41:03,202 - INFO - tod_display - tod_display.py:458 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:41:03,235 - INFO - tod_display - tod_display.py:462 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:41:05,860 - INFO - tod_display - tod_display.py:38 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 17:41:05,861 - INFO - tod_display - tod_display.py:48 - Using interactive plots: True
2025-07-08 17:41:06,187 - INFO - tod_display - tod_display.py:58 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 17:41:06,188 - INFO - tod_display - tod_display.py:68 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:41:06,305 - INFO - tod_display - tod_display.py:72 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:41:06,309 - INFO - tod_display - tod_display.py:300 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:06,502 - INFO - tod_display - tod_display.py:321 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 17:41:06,504 - INFO - tod_display - tod_display.py:330 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:41:06,962 - INFO - tod_display - tod_display.py:334 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:41:16,299 - INFO - tod_display - tod_display.py:100 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:41:17,944 - INFO - tod_display - tod_display.py:116 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:41:17,945 - INFO - tod_display - tod_display.py:126 - Using interactive plots: True
2025-07-08 17:41:17,945 - INFO - tod_display - tod_display.py:129 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:41:17,976 - INFO - tod_display - tod_display.py:133 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:41:17,977 - INFO - tod_display - tod_display.py:154 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:41:17,980 - INFO - tod_display - tod_display.py:174 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:41:17,981 - INFO - tod_display - tod_display.py:195 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:41:17,986 - INFO - tod_display - tod_display.py:248 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:41:17,992 - INFO - tod_display - tod_display.py:277 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:41:17,994 - INFO - tod_display - tod_display.py:354 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:17,996 - INFO - tod_display - tod_display.py:369 - Using interactive plots: True
2025-07-08 17:41:18,249 - INFO - tod_display - tod_display.py:381 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:41:18,250 - INFO - tod_display - tod_display.py:395 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:41:18,289 - INFO - tod_display - tod_display.py:399 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:41:18,293 - INFO - tod_display - tod_display.py:425 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:18,293 - INFO - tod_display - tod_display.py:440 - Using interactive plots: True
2025-07-08 17:41:18,503 - INFO - tod_display - tod_display.py:450 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:41:18,503 - INFO - tod_display - tod_display.py:460 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:41:18,544 - INFO - tod_display - tod_display.py:464 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:41:21,182 - INFO - tod_display - tod_display.py:40 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 17:41:21,182 - INFO - tod_display - tod_display.py:50 - Using interactive plots: True
2025-07-08 17:41:21,465 - INFO - tod_display - tod_display.py:60 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 17:41:21,466 - INFO - tod_display - tod_display.py:70 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:41:21,533 - INFO - tod_display - tod_display.py:74 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:41:21,537 - INFO - tod_display - tod_display.py:302 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:21,712 - INFO - tod_display - tod_display.py:323 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 17:41:21,712 - INFO - tod_display - tod_display.py:332 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:41:22,166 - INFO - tod_display - tod_display.py:336 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:41:56,596 - INFO - tod_display - tod_display.py:100 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:41:57,528 - INFO - tod_display - tod_display.py:116 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:41:57,528 - INFO - tod_display - tod_display.py:126 - Using interactive plots: True
2025-07-08 17:41:57,528 - INFO - tod_display - tod_display.py:129 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:41:57,551 - INFO - tod_display - tod_display.py:133 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:41:57,551 - INFO - tod_display - tod_display.py:154 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:41:57,552 - INFO - tod_display - tod_display.py:174 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:41:57,552 - INFO - tod_display - tod_display.py:195 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:41:57,552 - INFO - tod_display - tod_display.py:248 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:41:57,559 - INFO - tod_display - tod_display.py:277 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:41:57,563 - INFO - tod_display - tod_display.py:354 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:57,563 - INFO - tod_display - tod_display.py:369 - Using interactive plots: True
2025-07-08 17:41:57,772 - INFO - tod_display - tod_display.py:381 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:41:57,772 - INFO - tod_display - tod_display.py:395 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:41:57,828 - INFO - tod_display - tod_display.py:399 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:41:57,847 - INFO - tod_display - tod_display.py:425 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:57,848 - INFO - tod_display - tod_display.py:440 - Using interactive plots: True
2025-07-08 17:41:58,122 - INFO - tod_display - tod_display.py:450 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:41:58,122 - INFO - tod_display - tod_display.py:460 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:41:58,144 - INFO - tod_display - tod_display.py:464 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:41:59,810 - INFO - tod_display - tod_display.py:40 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 17:41:59,812 - INFO - tod_display - tod_display.py:50 - Using interactive plots: True
2025-07-08 17:42:00,163 - INFO - tod_display - tod_display.py:60 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 17:42:00,163 - INFO - tod_display - tod_display.py:70 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:42:00,219 - INFO - tod_display - tod_display.py:74 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:42:00,222 - INFO - tod_display - tod_display.py:302 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:42:00,480 - INFO - tod_display - tod_display.py:323 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 17:42:00,480 - INFO - tod_display - tod_display.py:332 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:42:00,773 - INFO - tod_display - tod_display.py:336 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:42:17,828 - INFO - tod_display - tod_display.py:100 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:42:18,759 - INFO - tod_display - tod_display.py:116 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:42:18,759 - INFO - tod_display - tod_display.py:126 - Using interactive plots: True
2025-07-08 17:42:18,760 - INFO - tod_display - tod_display.py:129 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:42:18,766 - INFO - tod_display - tod_display.py:133 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:42:18,766 - INFO - tod_display - tod_display.py:154 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:42:18,781 - INFO - tod_display - tod_display.py:174 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:42:18,782 - INFO - tod_display - tod_display.py:195 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:42:18,785 - INFO - tod_display - tod_display.py:248 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:42:18,788 - INFO - tod_display - tod_display.py:277 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:42:18,791 - INFO - tod_display - tod_display.py:354 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:42:18,791 - INFO - tod_display - tod_display.py:369 - Using interactive plots: True
2025-07-08 17:42:19,002 - INFO - tod_display - tod_display.py:381 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:42:19,002 - INFO - tod_display - tod_display.py:395 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:42:19,022 - INFO - tod_display - tod_display.py:399 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:42:19,025 - INFO - tod_display - tod_display.py:425 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:42:19,026 - INFO - tod_display - tod_display.py:440 - Using interactive plots: True
2025-07-08 17:42:19,230 - INFO - tod_display - tod_display.py:450 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:42:19,230 - INFO - tod_display - tod_display.py:460 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:42:19,250 - INFO - tod_display - tod_display.py:464 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:42:21,968 - INFO - tod_display - tod_display.py:40 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 17:42:21,968 - INFO - tod_display - tod_display.py:50 - Using interactive plots: True
2025-07-08 17:42:22,297 - INFO - tod_display - tod_display.py:60 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 17:42:22,297 - INFO - tod_display - tod_display.py:70 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:42:22,394 - INFO - tod_display - tod_display.py:74 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:42:22,403 - INFO - tod_display - tod_display.py:302 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:42:22,563 - INFO - tod_display - tod_display.py:323 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 17:42:22,563 - INFO - tod_display - tod_display.py:332 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:42:23,116 - INFO - tod_display - tod_display.py:336 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:42:34,459 - INFO - tod_display - tod_display.py:100 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:42:35,403 - INFO - tod_display - tod_display.py:116 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:42:35,403 - INFO - tod_display - tod_display.py:126 - Using interactive plots: True
2025-07-08 17:42:35,404 - INFO - tod_display - tod_display.py:129 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:42:35,423 - INFO - tod_display - tod_display.py:133 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:42:35,424 - INFO - tod_display - tod_display.py:154 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:42:35,426 - INFO - tod_display - tod_display.py:174 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:42:35,427 - INFO - tod_display - tod_display.py:195 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:42:35,429 - INFO - tod_display - tod_display.py:248 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:42:35,434 - INFO - tod_display - tod_display.py:277 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:42:35,437 - INFO - tod_display - tod_display.py:354 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:42:35,439 - INFO - tod_display - tod_display.py:369 - Using interactive plots: True
2025-07-08 17:42:35,672 - INFO - tod_display - tod_display.py:381 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:42:35,672 - INFO - tod_display - tod_display.py:395 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:42:35,691 - INFO - tod_display - tod_display.py:399 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:42:35,693 - INFO - tod_display - tod_display.py:425 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:42:35,693 - INFO - tod_display - tod_display.py:440 - Using interactive plots: True
2025-07-08 17:42:35,882 - INFO - tod_display - tod_display.py:450 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:42:35,882 - INFO - tod_display - tod_display.py:460 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:42:35,901 - INFO - tod_display - tod_display.py:464 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:42:37,348 - INFO - tod_display - tod_display.py:40 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 17:42:37,348 - INFO - tod_display - tod_display.py:50 - Using interactive plots: True
2025-07-08 17:42:37,624 - INFO - tod_display - tod_display.py:60 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 17:42:37,625 - INFO - tod_display - tod_display.py:70 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:42:37,677 - INFO - tod_display - tod_display.py:74 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:42:37,680 - INFO - tod_display - tod_display.py:302 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:42:37,851 - INFO - tod_display - tod_display.py:323 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 17:42:37,851 - INFO - tod_display - tod_display.py:332 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:42:38,168 - INFO - tod_display - tod_display.py:336 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:42:38,172 - INFO - tod_display - tod_display.py:490 - Starting ToD line chart display for plant: Kids Clinic India Limited
2025-07-08 17:42:38,173 - INFO - tod_display - tod_display.py:505 - Using interactive plots: True
2025-07-08 17:42:38,394 - INFO - tod_display - tod_display.py:515 - Successfully fetched 124 records for ToD line chart - Kids Clinic India Limited
2025-07-08 17:42:38,394 - INFO - tod_display - tod_display.py:525 - Creating interactive ToD line chart for Kids Clinic India Limited
2025-07-08 17:45:13,677 - INFO - tod_display - tod_display.py:100 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:45:14,569 - INFO - tod_display - tod_display.py:116 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:45:14,569 - INFO - tod_display - tod_display.py:126 - Using interactive plots: True
2025-07-08 17:45:14,569 - INFO - tod_display - tod_display.py:129 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:45:14,583 - INFO - tod_display - tod_display.py:133 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:45:14,583 - INFO - tod_display - tod_display.py:154 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:45:14,592 - INFO - tod_display - tod_display.py:174 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:45:14,594 - INFO - tod_display - tod_display.py:195 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:45:14,594 - INFO - tod_display - tod_display.py:248 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:45:14,601 - INFO - tod_display - tod_display.py:277 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:45:14,603 - INFO - tod_display - tod_display.py:354 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:45:14,604 - INFO - tod_display - tod_display.py:369 - Using interactive plots: True
2025-07-08 17:45:14,664 - WARNING - tod_display - tod_display.py:377 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:45:14,667 - INFO - tod_display - tod_display.py:425 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:45:14,667 - INFO - tod_display - tod_display.py:440 - Using interactive plots: True
2025-07-08 17:45:14,748 - WARNING - tod_display - tod_display.py:446 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:45:16,161 - INFO - tod_display - tod_display.py:40 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 17:45:16,161 - INFO - tod_display - tod_display.py:50 - Using interactive plots: True
2025-07-08 17:45:16,459 - INFO - tod_display - tod_display.py:60 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 17:45:16,475 - INFO - tod_display - tod_display.py:70 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:45:16,549 - INFO - tod_display - tod_display.py:74 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:45:16,551 - INFO - tod_display - tod_display.py:302 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:45:16,627 - WARNING - tod_display - tod_display.py:319 - No ToD generation vs consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:45:16,627 - INFO - tod_display - tod_display.py:490 - Starting ToD line chart display for plant: Kids Clinic India Limited
2025-07-08 17:45:16,627 - INFO - tod_display - tod_display.py:505 - Using interactive plots: True
2025-07-08 17:45:16,737 - WARNING - tod_display - tod_display.py:511 - No ToD line chart data available for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:45:35,593 - INFO - tod_display - tod_display.py:100 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:45:36,810 - INFO - tod_display - tod_display.py:116 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:45:36,909 - INFO - tod_display - tod_display.py:100 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:45:37,845 - INFO - tod_display - tod_display.py:116 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:45:37,845 - INFO - tod_display - tod_display.py:126 - Using interactive plots: True
2025-07-08 17:45:37,845 - INFO - tod_display - tod_display.py:129 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:45:37,865 - INFO - tod_display - tod_display.py:133 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:45:37,878 - INFO - tod_display - tod_display.py:154 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:45:37,881 - INFO - tod_display - tod_display.py:174 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:45:37,881 - INFO - tod_display - tod_display.py:195 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:45:37,882 - INFO - tod_display - tod_display.py:248 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:45:37,888 - INFO - tod_display - tod_display.py:277 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:45:37,890 - INFO - tod_display - tod_display.py:354 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:45:37,890 - INFO - tod_display - tod_display.py:369 - Using interactive plots: True
2025-07-08 17:45:38,078 - INFO - tod_display - tod_display.py:381 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:45:38,078 - INFO - tod_display - tod_display.py:395 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:45:38,103 - INFO - tod_display - tod_display.py:399 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:45:38,106 - INFO - tod_display - tod_display.py:425 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:45:38,106 - INFO - tod_display - tod_display.py:440 - Using interactive plots: True
2025-07-08 17:45:38,295 - INFO - tod_display - tod_display.py:450 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:45:38,295 - INFO - tod_display - tod_display.py:460 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:45:38,314 - INFO - tod_display - tod_display.py:464 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:45:39,878 - INFO - tod_display - tod_display.py:40 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 17:45:39,878 - INFO - tod_display - tod_display.py:50 - Using interactive plots: True
2025-07-08 17:45:40,144 - INFO - tod_display - tod_display.py:60 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 17:45:40,144 - INFO - tod_display - tod_display.py:70 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:45:40,195 - INFO - tod_display - tod_display.py:74 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:45:40,199 - INFO - tod_display - tod_display.py:302 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:45:40,371 - INFO - tod_display - tod_display.py:323 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 17:45:40,371 - INFO - tod_display - tod_display.py:332 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:45:40,640 - INFO - tod_display - tod_display.py:336 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:45:40,640 - INFO - tod_display - tod_display.py:490 - Starting ToD line chart display for plant: Kids Clinic India Limited
2025-07-08 17:45:40,646 - INFO - tod_display - tod_display.py:505 - Using interactive plots: True
2025-07-08 17:45:40,842 - INFO - tod_display - tod_display.py:515 - Successfully fetched 124 records for ToD line chart - Kids Clinic India Limited
2025-07-08 17:45:40,842 - INFO - tod_display - tod_display.py:525 - Creating interactive ToD line chart for Kids Clinic India Limited
2025-07-08 17:46:56,671 - INFO - tod_display - tod_display.py:100 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 17:46:57,657 - INFO - tod_display - tod_display.py:116 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:46:57,673 - INFO - tod_display - tod_display.py:126 - Using interactive plots: True
2025-07-08 17:46:57,673 - INFO - tod_display - tod_display.py:129 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:46:57,691 - INFO - tod_display - tod_display.py:133 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 17:46:57,691 - INFO - tod_display - tod_display.py:154 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 17:46:57,691 - INFO - tod_display - tod_display.py:174 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 17:46:57,691 - INFO - tod_display - tod_display.py:195 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 17:46:57,691 - INFO - tod_display - tod_display.py:248 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 17:46:57,703 - INFO - tod_display - tod_display.py:277 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 17:46:57,707 - INFO - tod_display - tod_display.py:354 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:46:57,707 - INFO - tod_display - tod_display.py:369 - Using interactive plots: True
2025-07-08 17:46:57,896 - INFO - tod_display - tod_display.py:381 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 17:46:57,896 - INFO - tod_display - tod_display.py:395 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:46:57,917 - INFO - tod_display - tod_display.py:399 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 17:46:57,920 - INFO - tod_display - tod_display.py:425 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:46:57,920 - INFO - tod_display - tod_display.py:440 - Using interactive plots: True
2025-07-08 17:46:58,130 - INFO - tod_display - tod_display.py:450 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 17:46:58,130 - INFO - tod_display - tod_display.py:460 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:46:58,149 - INFO - tod_display - tod_display.py:464 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 17:46:59,600 - INFO - tod_display - tod_display.py:40 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 17:46:59,600 - INFO - tod_display - tod_display.py:50 - Using interactive plots: True
2025-07-08 17:46:59,934 - INFO - tod_display - tod_display.py:60 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 17:46:59,934 - INFO - tod_display - tod_display.py:70 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:46:59,978 - INFO - tod_display - tod_display.py:74 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 17:46:59,981 - INFO - tod_display - tod_display.py:302 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:47:00,163 - INFO - tod_display - tod_display.py:323 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 17:47:00,163 - INFO - tod_display - tod_display.py:332 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:47:00,418 - INFO - tod_display - tod_display.py:336 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 17:47:00,418 - INFO - tod_display - tod_display.py:490 - Starting ToD line chart display for plant: Kids Clinic India Limited
2025-07-08 17:47:00,418 - INFO - tod_display - tod_display.py:505 - Using interactive plots: True
2025-07-08 17:47:00,641 - INFO - tod_display - tod_display.py:515 - Successfully fetched 124 records for ToD line chart - Kids Clinic India Limited
2025-07-08 17:47:00,641 - INFO - tod_display - tod_display.py:525 - Creating interactive ToD line chart for Kids Clinic India Limited
2025-07-08 17:47:00,798 - INFO - tod_display - tod_display.py:529 - Successfully displayed interactive ToD line chart for Kids Clinic India Limited
2025-07-08 18:02:24,153 - INFO - tod_display - tod_display.py:100 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-08 18:02:25,132 - INFO - tod_display - tod_display.py:116 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-08 18:02:25,132 - INFO - tod_display - tod_display.py:126 - Using interactive plots: True
2025-07-08 18:02:25,132 - INFO - tod_display - tod_display.py:129 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 18:02:25,151 - INFO - tod_display - tod_display.py:133 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-08 18:02:25,151 - INFO - tod_display - tod_display.py:154 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-08 18:02:25,154 - INFO - tod_display - tod_display.py:174 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-08 18:02:25,154 - INFO - tod_display - tod_display.py:195 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-08 18:02:25,157 - INFO - tod_display - tod_display.py:248 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-08 18:02:25,161 - INFO - tod_display - tod_display.py:277 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-08 18:02:25,164 - INFO - tod_display - tod_display.py:354 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 18:02:25,164 - INFO - tod_display - tod_display.py:369 - Using interactive plots: True
2025-07-08 18:02:25,355 - INFO - tod_display - tod_display.py:381 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-08 18:02:25,355 - INFO - tod_display - tod_display.py:395 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 18:02:25,389 - INFO - tod_display - tod_display.py:399 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-08 18:02:25,413 - INFO - tod_display - tod_display.py:425 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 18:02:25,413 - INFO - tod_display - tod_display.py:440 - Using interactive plots: True
2025-07-08 18:02:25,654 - INFO - tod_display - tod_display.py:450 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-08 18:02:25,654 - INFO - tod_display - tod_display.py:460 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 18:02:25,676 - INFO - tod_display - tod_display.py:464 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-08 18:02:27,297 - INFO - tod_display - tod_display.py:40 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-08 18:02:27,297 - INFO - tod_display - tod_display.py:50 - Using interactive plots: True
2025-07-08 18:02:27,574 - INFO - tod_display - tod_display.py:60 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-08 18:02:27,574 - INFO - tod_display - tod_display.py:70 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 18:02:27,620 - INFO - tod_display - tod_display.py:74 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-08 18:02:27,623 - INFO - tod_display - tod_display.py:302 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 18:02:27,833 - INFO - tod_display - tod_display.py:323 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-08 18:02:27,834 - INFO - tod_display - tod_display.py:332 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 18:02:28,116 - INFO - tod_display - tod_display.py:336 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-08 18:02:28,123 - INFO - tod_display - tod_display.py:490 - Starting ToD line chart display for plant: Kids Clinic India Limited
2025-07-08 18:02:28,123 - INFO - tod_display - tod_display.py:505 - Using interactive plots: True
2025-07-08 18:02:28,334 - INFO - tod_display - tod_display.py:515 - Successfully fetched 124 records for ToD line chart - Kids Clinic India Limited
2025-07-08 18:02:28,335 - INFO - tod_display - tod_display.py:525 - Creating interactive ToD line chart for Kids Clinic India Limited
2025-07-08 18:02:28,355 - INFO - tod_display - tod_display.py:529 - Successfully displayed interactive ToD line chart for Kids Clinic India Limited
2025-07-09 10:33:01,603 - INFO - tod_display - tod_display.py:102 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-09 10:33:03,271 - INFO - tod_display - tod_display.py:118 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-09 10:33:03,272 - INFO - tod_display - tod_display.py:128 - Using interactive plots: True
2025-07-09 10:33:03,272 - INFO - tod_display - tod_display.py:131 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-09 10:33:03,356 - INFO - tod_display - tod_display.py:135 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-09 10:33:03,357 - INFO - tod_display - tod_display.py:156 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-09 10:33:03,360 - INFO - tod_display - tod_display.py:176 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-09 10:33:03,361 - INFO - tod_display - tod_display.py:197 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-09 10:33:03,369 - INFO - tod_display - tod_display.py:250 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-09 10:33:03,411 - INFO - tod_display - tod_display.py:279 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-09 10:33:03,416 - INFO - tod_display - tod_display.py:356 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:33:03,417 - INFO - tod_display - tod_display.py:371 - Using interactive plots: True
2025-07-09 10:33:03,520 - WARNING - tod_display - tod_display.py:379 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:33:03,524 - INFO - tod_display - tod_display.py:427 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:33:03,525 - INFO - tod_display - tod_display.py:442 - Using interactive plots: True
2025-07-09 10:33:03,609 - WARNING - tod_display - tod_display.py:448 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:33:06,945 - INFO - tod_display - tod_display.py:42 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-09 10:33:06,946 - INFO - tod_display - tod_display.py:52 - Using interactive plots: True
2025-07-09 10:33:07,326 - INFO - tod_display - tod_display.py:62 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-09 10:33:07,327 - INFO - tod_display - tod_display.py:72 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-09 10:33:08,336 - INFO - tod_display - tod_display.py:102 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-09 10:33:10,014 - INFO - tod_display - tod_display.py:118 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-09 10:33:10,014 - INFO - tod_display - tod_display.py:128 - Using interactive plots: True
2025-07-09 10:33:10,014 - INFO - tod_display - tod_display.py:131 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-09 10:33:10,055 - INFO - tod_display - tod_display.py:135 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-09 10:33:10,055 - INFO - tod_display - tod_display.py:156 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-09 10:33:10,063 - INFO - tod_display - tod_display.py:176 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-09 10:33:10,063 - INFO - tod_display - tod_display.py:197 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-09 10:33:10,069 - INFO - tod_display - tod_display.py:250 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-09 10:33:10,074 - INFO - tod_display - tod_display.py:279 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-09 10:33:10,078 - INFO - tod_display - tod_display.py:356 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-09 10:33:10,079 - INFO - tod_display - tod_display.py:371 - Using interactive plots: True
2025-07-09 10:33:10,253 - INFO - tod_display - tod_display.py:383 - Successfully fetched 120 records for ToD generation - Kids Clinic India Limited
2025-07-09 10:33:10,253 - INFO - tod_display - tod_display.py:397 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-09 10:33:10,349 - INFO - tod_display - tod_display.py:401 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-09 10:33:10,355 - INFO - tod_display - tod_display.py:427 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-09 10:33:10,356 - INFO - tod_display - tod_display.py:442 - Using interactive plots: True
2025-07-09 10:33:10,532 - INFO - tod_display - tod_display.py:452 - Successfully fetched 120 records for ToD consumption - Kids Clinic India Limited
2025-07-09 10:33:10,532 - INFO - tod_display - tod_display.py:462 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-09 10:33:10,577 - INFO - tod_display - tod_display.py:466 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-09 10:33:13,892 - INFO - tod_display - tod_display.py:42 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-09 10:33:13,892 - INFO - tod_display - tod_display.py:52 - Using interactive plots: True
2025-07-09 10:33:14,220 - INFO - tod_display - tod_display.py:62 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-09 10:33:14,220 - INFO - tod_display - tod_display.py:72 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-09 10:33:14,335 - INFO - tod_display - tod_display.py:76 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-09 10:33:14,335 - INFO - tod_display - tod_display.py:304 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-09 10:33:14,686 - INFO - tod_display - tod_display.py:325 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-09 10:33:14,686 - INFO - tod_display - tod_display.py:334 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-09 10:33:15,404 - INFO - tod_display - tod_display.py:338 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-09 10:33:15,404 - INFO - tod_display - tod_display.py:492 - Starting ToD line chart display for plant: Kids Clinic India Limited
2025-07-09 10:33:15,404 - INFO - tod_display - tod_display.py:507 - Using interactive plots: True
2025-07-09 10:33:15,648 - INFO - tod_display - tod_display.py:517 - Successfully fetched 120 records for ToD line chart - Kids Clinic India Limited
2025-07-09 10:33:15,648 - INFO - tod_display - tod_display.py:527 - Creating interactive ToD line chart for Kids Clinic India Limited
2025-07-09 10:33:15,797 - INFO - tod_display - tod_display.py:531 - Successfully displayed interactive ToD line chart for Kids Clinic India Limited
2025-07-09 10:33:15,801 - INFO - tod_display - tod_display.py:735 - Starting generation vs consumption settled heatmap display for plant: Kids Clinic India Limited
2025-07-09 10:33:15,801 - INFO - tod_display - tod_display.py:751 - Fetching heatmap data for Kids Clinic India Limited
2025-07-09 10:33:16,270 - INFO - tod_display - tod_display.py:759 - Successfully fetched 643 rows of heatmap data for Kids Clinic India Limited
2025-07-09 10:33:16,270 - INFO - tod_display - tod_display.py:768 - Creating generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 10:33:16,387 - INFO - tod_display - tod_display.py:772 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 10:33:21,889 - INFO - tod_display - tod_display.py:102 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-09 10:33:23,628 - INFO - tod_display - tod_display.py:118 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-09 10:33:23,628 - INFO - tod_display - tod_display.py:128 - Using interactive plots: True
2025-07-09 10:33:23,628 - INFO - tod_display - tod_display.py:131 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-09 10:33:23,663 - INFO - tod_display - tod_display.py:135 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-09 10:33:23,663 - INFO - tod_display - tod_display.py:156 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-09 10:33:23,663 - INFO - tod_display - tod_display.py:176 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-09 10:33:23,663 - INFO - tod_display - tod_display.py:197 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-09 10:33:23,670 - INFO - tod_display - tod_display.py:250 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-09 10:33:23,674 - INFO - tod_display - tod_display.py:279 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-09 10:33:23,679 - INFO - tod_display - tod_display.py:356 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:33:23,679 - INFO - tod_display - tod_display.py:371 - Using interactive plots: True
2025-07-09 10:33:23,768 - WARNING - tod_display - tod_display.py:379 - No ToD generation data available for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:33:23,771 - INFO - tod_display - tod_display.py:427 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:33:23,772 - INFO - tod_display - tod_display.py:442 - Using interactive plots: True
2025-07-09 10:33:23,848 - WARNING - tod_display - tod_display.py:448 - No ToD consumption data available for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:33:27,826 - INFO - tod_display - tod_display.py:102 - Starting monthly banking settlement display for plant: Kids Clinic India Limited
2025-07-09 10:33:29,456 - INFO - tod_display - tod_display.py:118 - Successfully fetched 6 records for monthly banking settlement - Kids Clinic India Limited
2025-07-09 10:33:29,457 - INFO - tod_display - tod_display.py:128 - Using interactive plots: True
2025-07-09 10:33:29,458 - INFO - tod_display - tod_display.py:131 - Creating interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-09 10:33:29,508 - INFO - tod_display - tod_display.py:135 - Successfully displayed interactive monthly banking settlement chart for Kids Clinic India Limited
2025-07-09 10:33:29,509 - INFO - tod_display - tod_display.py:156 - Processing metrics for monthly banking settlement - Kids Clinic India Limited
2025-07-09 10:33:29,510 - INFO - tod_display - tod_display.py:176 - Successfully calculated derived metrics for Kids Clinic India Limited
2025-07-09 10:33:29,510 - INFO - tod_display - tod_display.py:197 - Successfully calculated totals for metric boxes - Kids Clinic India Limited
2025-07-09 10:33:29,510 - INFO - tod_display - tod_display.py:250 - Successfully displayed metrics for Kids Clinic India Limited
2025-07-09 10:33:29,526 - INFO - tod_display - tod_display.py:279 - Successfully displayed summary dataframe for Kids Clinic India Limited
2025-07-09 10:33:29,529 - INFO - tod_display - tod_display.py:356 - Starting ToD generation display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-09 10:33:29,530 - INFO - tod_display - tod_display.py:371 - Using interactive plots: True
2025-07-09 10:33:29,710 - INFO - tod_display - tod_display.py:383 - Successfully fetched 124 records for ToD generation - Kids Clinic India Limited
2025-07-09 10:33:29,710 - INFO - tod_display - tod_display.py:397 - Creating interactive ToD generation plot for Kids Clinic India Limited
2025-07-09 10:33:29,746 - INFO - tod_display - tod_display.py:401 - Successfully displayed interactive ToD generation plot for Kids Clinic India Limited
2025-07-09 10:33:29,751 - INFO - tod_display - tod_display.py:427 - Starting ToD consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-09 10:33:29,751 - INFO - tod_display - tod_display.py:442 - Using interactive plots: True
2025-07-09 10:33:29,962 - INFO - tod_display - tod_display.py:452 - Successfully fetched 124 records for ToD consumption - Kids Clinic India Limited
2025-07-09 10:33:29,962 - INFO - tod_display - tod_display.py:462 - Creating interactive ToD consumption plot for Kids Clinic India Limited
2025-07-09 10:33:30,006 - INFO - tod_display - tod_display.py:466 - Successfully displayed interactive ToD consumption plot for Kids Clinic India Limited
2025-07-09 10:33:32,634 - INFO - tod_display - tod_display.py:42 - Starting monthly ToD before banking display for plant: Kids Clinic India Limited
2025-07-09 10:33:32,634 - INFO - tod_display - tod_display.py:52 - Using interactive plots: True
2025-07-09 10:33:32,938 - INFO - tod_display - tod_display.py:62 - Successfully fetched 643 records for monthly ToD before banking - Kids Clinic India Limited
2025-07-09 10:33:32,939 - INFO - tod_display - tod_display.py:72 - Creating interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-09 10:33:33,050 - INFO - tod_display - tod_display.py:76 - Successfully displayed interactive monthly ToD before banking plot for Kids Clinic India Limited
2025-07-09 10:33:33,057 - INFO - tod_display - tod_display.py:304 - Starting ToD generation vs consumption display for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-09 10:33:33,224 - INFO - tod_display - tod_display.py:325 - Successfully fetched 4 records for ToD generation vs consumption - Kids Clinic India Limited
2025-07-09 10:33:33,224 - INFO - tod_display - tod_display.py:334 - Creating ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-09 10:33:33,787 - INFO - tod_display - tod_display.py:338 - Successfully displayed ToD generation vs consumption plot for Kids Clinic India Limited
2025-07-09 10:33:33,787 - INFO - tod_display - tod_display.py:492 - Starting ToD line chart display for plant: Kids Clinic India Limited
2025-07-09 10:33:33,787 - INFO - tod_display - tod_display.py:507 - Using interactive plots: True
2025-07-09 10:33:33,989 - INFO - tod_display - tod_display.py:517 - Successfully fetched 124 records for ToD line chart - Kids Clinic India Limited
2025-07-09 10:33:33,989 - INFO - tod_display - tod_display.py:527 - Creating interactive ToD line chart for Kids Clinic India Limited
2025-07-09 10:33:34,022 - INFO - tod_display - tod_display.py:531 - Successfully displayed interactive ToD line chart for Kids Clinic India Limited
2025-07-09 10:33:34,022 - INFO - tod_display - tod_display.py:735 - Starting generation vs consumption settled heatmap display for plant: Kids Clinic India Limited
2025-07-09 10:33:34,022 - INFO - tod_display - tod_display.py:751 - Fetching heatmap data for Kids Clinic India Limited
2025-07-09 10:33:34,485 - INFO - tod_display - tod_display.py:759 - Successfully fetched 643 rows of heatmap data for Kids Clinic India Limited
2025-07-09 10:33:34,485 - INFO - tod_display - tod_display.py:768 - Creating generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 10:33:34,521 - INFO - tod_display - tod_display.py:772 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
