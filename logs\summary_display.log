2025-07-08 12:02:36,753 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:36,753 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 12:02:36,950 - WARNING - summary_display - summary_display.py:181 - No chart data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 12:02:36,950 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 12:02:36,950 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 12:02:37,235 - WARNING - summary_display - summary_display.py:273 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 12:02:37,237 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:37,435 - WARNING - summary_display - summary_display.py:504 - No unitwise data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 12:02:44,933 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-01
2025-07-08 12:02:44,933 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 12:02:45,149 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 12:02:45,215 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 12:02:45,468 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 12:02:45,468 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 29.77 MWh, Consumption: 22.91 MWh
2025-07-08 12:02:45,477 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-01, hourly_aggregation: False
2025-07-08 12:02:45,477 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 12:02:45,674 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 1056 records
2025-07-08 12:02:46,189 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:02:46,189 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 12:02:46,717 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 30 records
2025-07-08 12:02:46,750 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 12:02:47,276 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 30 records
2025-07-08 12:02:47,281 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 813.46 MWh, Consumption: 687.42 MWh
2025-07-08 12:02:47,281 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-08 12:02:47,281 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 12:02:47,799 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 12:02:47,855 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 12:02:47,857 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:02:48,541 - INFO - summary_display - summary_display.py:429 - Successfully fetched unitwise data with location, 11 records
2025-07-08 12:02:48,541 - INFO - summary_display - summary_display.py:434 - Interactive plot setting: True
2025-07-08 12:02:49,015 - INFO - summary_display - summary_display.py:449 - Successfully fetched pie chart data with 11 records
2025-07-08 12:02:49,056 - INFO - summary_display - summary_display.py:462 - Successfully displayed interactive pie chart
2025-07-08 12:02:49,056 - INFO - summary_display - summary_display.py:495 - Successfully displayed merged table with 11 rows
2025-07-08 12:28:13,417 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:13,418 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 12:28:13,653 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 30 records
2025-07-08 12:28:13,687 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 12:28:13,892 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 30 records
2025-07-08 12:28:13,894 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 813.46 MWh, Consumption: 687.42 MWh
2025-07-08 12:28:13,901 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-08 12:28:13,901 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 12:28:14,109 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 12:28:14,149 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 12:28:14,152 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:14,450 - INFO - summary_display - summary_display.py:429 - Successfully fetched unitwise data with location, 11 records
2025-07-08 12:28:14,450 - INFO - summary_display - summary_display.py:434 - Interactive plot setting: True
2025-07-08 12:28:14,629 - INFO - summary_display - summary_display.py:449 - Successfully fetched pie chart data with 11 records
2025-07-08 12:28:14,650 - INFO - summary_display - summary_display.py:462 - Successfully displayed interactive pie chart
2025-07-08 12:28:14,654 - INFO - summary_display - summary_display.py:495 - Successfully displayed merged table with 11 rows
2025-07-08 12:28:25,581 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:25,581 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 12:28:25,929 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 30 records
2025-07-08 12:28:26,063 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 12:28:26,384 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 30 records
2025-07-08 12:28:26,384 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 813.46 MWh, Consumption: 687.42 MWh
2025-07-08 12:28:26,394 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-08 12:28:26,397 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 12:28:26,729 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 12:28:26,813 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 12:28:26,813 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:27,162 - INFO - summary_display - summary_display.py:429 - Successfully fetched unitwise data with location, 11 records
2025-07-08 12:28:27,162 - INFO - summary_display - summary_display.py:434 - Interactive plot setting: True
2025-07-08 12:28:27,350 - INFO - summary_display - summary_display.py:449 - Successfully fetched pie chart data with 11 records
2025-07-08 12:28:27,379 - INFO - summary_display - summary_display.py:462 - Successfully displayed interactive pie chart
2025-07-08 12:28:27,389 - INFO - summary_display - summary_display.py:495 - Successfully displayed merged table with 11 rows
2025-07-08 14:54:06,042 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:06,042 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 14:54:06,131 - WARNING - summary_display - summary_display.py:181 - No chart data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 14:54:06,147 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 14:54:06,147 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 14:54:06,253 - WARNING - summary_display - summary_display.py:273 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 14:54:06,253 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:06,383 - WARNING - summary_display - summary_display.py:504 - No unitwise data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 14:54:11,385 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-01
2025-07-08 14:54:11,385 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 14:54:11,497 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 14:54:11,613 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 14:54:11,768 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 14:54:11,769 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 29.77 MWh, Consumption: 22.91 MWh
2025-07-08 14:54:11,776 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-01, hourly_aggregation: False
2025-07-08 14:54:11,776 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 14:54:11,895 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 1056 records
2025-07-08 14:54:11,941 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 14:54:11,947 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-01
2025-07-08 14:54:12,040 - INFO - summary_display - summary_display.py:429 - Successfully fetched unitwise data with location, 11 records
2025-07-08 14:54:12,040 - INFO - summary_display - summary_display.py:434 - Interactive plot setting: True
2025-07-08 14:54:12,134 - INFO - summary_display - summary_display.py:449 - Successfully fetched pie chart data with 11 records
2025-07-08 14:54:12,170 - INFO - summary_display - summary_display.py:462 - Successfully displayed interactive pie chart
2025-07-08 14:54:12,177 - INFO - summary_display - summary_display.py:495 - Successfully displayed merged table with 11 rows
2025-07-08 14:54:12,355 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 14:54:12,355 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 14:54:12,646 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 30 records
2025-07-08 14:54:12,818 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 14:54:13,034 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 30 records
2025-07-08 14:54:13,034 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 813.46 MWh, Consumption: 687.42 MWh
2025-07-08 14:54:13,041 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-08 14:54:13,041 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 14:54:13,252 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 14:54:13,318 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 14:54:13,332 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 14:54:13,602 - INFO - summary_display - summary_display.py:429 - Successfully fetched unitwise data with location, 11 records
2025-07-08 14:54:13,602 - INFO - summary_display - summary_display.py:434 - Interactive plot setting: True
2025-07-08 14:54:13,762 - INFO - summary_display - summary_display.py:449 - Successfully fetched pie chart data with 11 records
2025-07-08 14:54:13,811 - INFO - summary_display - summary_display.py:462 - Successfully displayed interactive pie chart
2025-07-08 14:54:13,812 - INFO - summary_display - summary_display.py:495 - Successfully displayed merged table with 11 rows
2025-07-08 15:48:10,662 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:10,662 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:48:10,757 - WARNING - summary_display - summary_display.py:181 - No chart data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 15:48:12,810 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 15:48:12,810 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 15:48:12,920 - WARNING - summary_display - summary_display.py:273 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 15:48:13,899 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-01
2025-07-08 15:48:13,900 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:48:14,125 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 15:48:14,269 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 15:48:14,381 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 15:48:14,381 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 27.38 MWh, Consumption: 21.14 MWh
2025-07-08 15:48:14,802 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:48:14,804 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:48:15,187 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 15:48:15,237 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 15:48:15,536 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 15:48:15,552 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 15:48:18,304 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 15:48:18,304 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 15:48:18,537 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 15:48:19,121 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 15:50:57,185 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:50:57,185 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:50:57,379 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 15:50:57,420 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 15:50:57,615 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 15:50:57,615 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 15:50:59,133 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 15:50:59,134 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 15:50:59,366 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 15:50:59,415 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 15:51:15,507 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:51:15,508 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:51:15,751 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 15:51:15,788 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 15:51:15,984 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 15:51:15,984 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 15:51:17,507 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 15:51:17,508 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 15:51:17,741 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 15:51:17,783 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 15:53:06,336 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:53:06,337 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:53:06,594 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 15:53:06,632 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 15:53:06,910 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 15:53:06,910 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 15:53:08,382 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 15:53:08,382 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 15:53:08,644 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 15:53:08,694 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 16:03:09,043 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:03:09,043 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:03:09,260 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 16:03:09,297 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:03:09,549 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 16:03:09,549 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:03:11,131 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:03:11,131 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 16:03:11,390 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 16:03:11,426 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 16:04:53,516 - INFO - summary_display - summary_display.py:332 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:04:53,516 - INFO - summary_display - summary_display.py:337 - Interactive plot setting: True
2025-07-08 16:04:53,753 - INFO - summary_display - summary_display.py:352 - Successfully fetched hourly data with 24 records
2025-07-08 16:04:53,810 - INFO - summary_display - summary_display.py:366 - Successfully displayed interactive hourly chart
2025-07-08 16:05:50,237 - INFO - summary_display - summary_display.py:332 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:05:50,238 - INFO - summary_display - summary_display.py:337 - Interactive plot setting: True
2025-07-08 16:05:50,509 - INFO - summary_display - summary_display.py:352 - Successfully fetched hourly data with 24 records
2025-07-08 16:05:50,549 - INFO - summary_display - summary_display.py:366 - Successfully displayed interactive hourly chart
2025-07-08 16:07:18,767 - INFO - summary_display - summary_display.py:336 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:07:18,767 - INFO - summary_display - summary_display.py:341 - Interactive plot setting: True
2025-07-08 16:07:19,056 - INFO - summary_display - summary_display.py:356 - Successfully fetched hourly data with 24 records
2025-07-08 16:07:19,095 - INFO - summary_display - summary_display.py:370 - Successfully displayed interactive hourly chart
2025-07-08 16:07:50,688 - INFO - summary_display - summary_display.py:340 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:07:50,688 - INFO - summary_display - summary_display.py:345 - Interactive plot setting: True
2025-07-08 16:07:51,074 - INFO - summary_display - summary_display.py:360 - Successfully fetched hourly data with 24 records
2025-07-08 16:07:51,130 - INFO - summary_display - summary_display.py:374 - Successfully displayed interactive hourly chart
2025-07-08 16:07:54,554 - INFO - summary_display - summary_display.py:341 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:07:54,554 - INFO - summary_display - summary_display.py:346 - Interactive plot setting: True
2025-07-08 16:07:54,791 - INFO - summary_display - summary_display.py:361 - Successfully fetched hourly data with 24 records
2025-07-08 16:07:54,831 - INFO - summary_display - summary_display.py:375 - Successfully displayed interactive hourly chart
2025-07-08 16:08:30,816 - INFO - summary_display - summary_display.py:341 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:08:30,816 - INFO - summary_display - summary_display.py:346 - Interactive plot setting: True
2025-07-08 16:08:31,023 - INFO - summary_display - summary_display.py:361 - Successfully fetched hourly data with 24 records
2025-07-08 16:08:31,064 - INFO - summary_display - summary_display.py:375 - Successfully displayed interactive hourly chart
2025-07-08 16:09:44,161 - INFO - summary_display - summary_display.py:345 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:09:44,162 - INFO - summary_display - summary_display.py:350 - Interactive plot setting: True
2025-07-08 16:09:44,435 - INFO - summary_display - summary_display.py:365 - Successfully fetched hourly data with 24 records
2025-07-08 16:09:44,494 - INFO - summary_display - summary_display.py:379 - Successfully displayed interactive hourly chart
2025-07-08 16:10:37,047 - INFO - summary_display - summary_display.py:221 - Displaying monthly energy data for None: 2024-07-01 to 2025-07-08
2025-07-08 16:10:38,739 - INFO - summary_display - summary_display.py:345 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:10:38,739 - INFO - summary_display - summary_display.py:350 - Interactive plot setting: True
2025-07-08 16:10:39,053 - INFO - summary_display - summary_display.py:365 - Successfully fetched hourly data with 24 records
2025-07-08 16:10:39,096 - INFO - summary_display - summary_display.py:379 - Successfully displayed interactive hourly chart
2025-07-08 16:12:02,961 - INFO - summary_display - summary_display.py:221 - Displaying monthly energy data for None: 2024-07-01 to 2025-07-08
2025-07-08 16:12:35,573 - INFO - summary_display - summary_display.py:221 - Displaying monthly energy data for Kids Clinic India Limited: 2024-07-01 to 2025-07-08
2025-07-08 16:12:35,946 - ERROR - summary_display - summary_display.py:315 - Critical error in display_generation_vs_consumption: time data "%Y-%m" doesn't match format "%Y-%m", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
2025-07-08 16:19:02,437 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-01
2025-07-08 16:19:02,438 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:19:02,576 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 16:19:02,607 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:19:02,726 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 16:19:02,726 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 27.38 MWh, Consumption: 21.14 MWh
2025-07-08 16:19:20,256 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-01
2025-07-08 16:19:20,256 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:19:20,405 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 16:19:20,427 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:19:20,561 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 16:19:20,562 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 27.38 MWh, Consumption: 21.14 MWh
2025-07-08 16:19:36,597 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-01
2025-07-08 16:19:36,598 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:19:36,745 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 16:19:36,830 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:19:36,968 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 16:19:36,969 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 27.38 MWh, Consumption: 21.14 MWh
2025-07-08 16:20:41,154 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:20:41,154 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:20:41,366 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 16:20:41,408 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:20:41,700 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 16:20:41,700 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:30:00,835 - INFO - summary_display - summary_display.py:51 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 16:30:00,836 - INFO - summary_display - summary_display.py:56 - Interactive plot setting: True
2025-07-08 16:30:00,942 - WARNING - summary_display - summary_display.py:183 - No chart data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 16:30:04,424 - INFO - summary_display - summary_display.py:51 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-01
2025-07-08 16:30:04,424 - INFO - summary_display - summary_display.py:56 - Interactive plot setting: True
2025-07-08 16:30:04,526 - INFO - summary_display - summary_display.py:66 - Successfully fetched chart data with 1056 records
2025-07-08 16:30:04,841 - INFO - summary_display - summary_display.py:81 - Successfully displayed interactive chart
2025-07-08 16:30:04,952 - INFO - summary_display - summary_display.py:109 - Successfully fetched metrics data with 1056 records
2025-07-08 16:30:04,953 - INFO - summary_display - summary_display.py:130 - Calculated metrics - Generation: 27.38 MWh, Consumption: 21.14 MWh
2025-07-08 16:30:05,621 - INFO - summary_display - summary_display.py:51 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:30:05,621 - INFO - summary_display - summary_display.py:56 - Interactive plot setting: True
2025-07-08 16:30:05,859 - INFO - summary_display - summary_display.py:66 - Successfully fetched chart data with 31 records
2025-07-08 16:30:05,919 - INFO - summary_display - summary_display.py:81 - Successfully displayed interactive chart
2025-07-08 16:30:06,205 - INFO - summary_display - summary_display.py:109 - Successfully fetched metrics data with 31 records
2025-07-08 16:30:06,214 - INFO - summary_display - summary_display.py:130 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:31:47,329 - INFO - summary_display - summary_display.py:523 - Displaying monthly FY supplied vs consumed for plant: Kids Clinic India Limited
2025-07-08 16:31:47,723 - INFO - summary_display - summary_display.py:530 - Successfully fetched monthly FY data with 6 months
2025-07-08 16:31:47,807 - INFO - summary_display - summary_display.py:577 - Successfully displayed monthly FY chart
2025-07-08 16:32:18,141 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:32:18,141 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:32:18,422 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 16:32:18,463 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:32:18,801 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 16:32:18,802 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:36:21,171 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:36:21,172 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:36:21,384 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 16:36:21,419 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:36:21,602 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 16:36:21,602 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:36:28,919 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:36:28,920 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:36:29,120 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 16:36:29,170 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:36:29,385 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 16:36:29,385 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:37:05,659 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:37:05,659 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:37:05,922 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 16:37:05,973 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:37:06,242 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 16:37:06,242 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:37:14,801 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:37:14,801 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:37:15,057 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 31 records
2025-07-08 16:37:15,101 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:37:15,321 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 31 records
2025-07-08 16:37:15,322 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:37:25,159 - INFO - summary_display - summary_display.py:52 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:37:25,160 - INFO - summary_display - summary_display.py:57 - Interactive plot setting: True
2025-07-08 16:37:25,410 - INFO - summary_display - summary_display.py:67 - Successfully fetched chart data with 31 records
2025-07-08 16:37:25,443 - INFO - summary_display - summary_display.py:82 - Successfully displayed interactive chart
2025-07-08 16:37:25,639 - INFO - summary_display - summary_display.py:110 - Successfully fetched metrics data with 31 records
2025-07-08 16:37:25,640 - INFO - summary_display - summary_display.py:131 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:37:48,286 - INFO - summary_display - summary_display.py:52 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:37:48,287 - INFO - summary_display - summary_display.py:57 - Interactive plot setting: True
2025-07-08 16:37:48,558 - INFO - summary_display - summary_display.py:67 - Successfully fetched chart data with 31 records
2025-07-08 16:37:48,593 - INFO - summary_display - summary_display.py:82 - Successfully displayed interactive chart
2025-07-08 16:37:48,775 - INFO - summary_display - summary_display.py:110 - Successfully fetched metrics data with 31 records
2025-07-08 16:37:48,775 - INFO - summary_display - summary_display.py:131 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:37:57,070 - INFO - summary_display - summary_display.py:52 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:37:57,070 - INFO - summary_display - summary_display.py:57 - Interactive plot setting: True
2025-07-08 16:37:57,385 - INFO - summary_display - summary_display.py:67 - Successfully fetched chart data with 31 records
2025-07-08 16:37:57,426 - INFO - summary_display - summary_display.py:82 - Successfully displayed interactive chart
2025-07-08 16:37:57,687 - INFO - summary_display - summary_display.py:110 - Successfully fetched metrics data with 31 records
2025-07-08 16:37:57,689 - INFO - summary_display - summary_display.py:131 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:38:08,570 - INFO - summary_display - summary_display.py:52 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:38:08,571 - INFO - summary_display - summary_display.py:57 - Interactive plot setting: True
2025-07-08 16:38:08,844 - INFO - summary_display - summary_display.py:67 - Successfully fetched chart data with 31 records
2025-07-08 16:38:08,867 - INFO - summary_display - summary_display.py:82 - Successfully displayed interactive chart
2025-07-08 16:38:09,116 - INFO - summary_display - summary_display.py:110 - Successfully fetched metrics data with 31 records
2025-07-08 16:38:09,116 - INFO - summary_display - summary_display.py:131 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:38:09,116 - INFO - summary_display - summary_display.py:197 - Displaying last 12 months generation vs consumption for plant: Kids Clinic India Limited
2025-07-08 16:38:09,116 - INFO - summary_display - summary_display.py:202 - Interactive plot setting: True
2025-07-08 16:38:09,596 - INFO - summary_display - summary_display.py:212 - Successfully fetched last 12 months data with 6 records
2025-07-08 16:38:09,817 - INFO - summary_display - summary_display.py:224 - Successfully displayed interactive last 12 months chart
2025-07-08 16:38:09,827 - INFO - summary_display - summary_display.py:278 - Successfully displayed last 12 months summary metrics
2025-07-08 16:38:48,691 - INFO - summary_display - summary_display.py:52 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 16:38:48,710 - INFO - summary_display - summary_display.py:57 - Interactive plot setting: True
2025-07-08 16:38:48,898 - WARNING - summary_display - summary_display.py:184 - No chart data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 16:38:48,953 - INFO - summary_display - summary_display.py:197 - Displaying last 12 months generation vs consumption for plant: Kids Clinic India Limited
2025-07-08 16:38:48,986 - INFO - summary_display - summary_display.py:202 - Interactive plot setting: True
2025-07-08 16:38:49,831 - INFO - summary_display - summary_display.py:212 - Successfully fetched last 12 months data with 6 records
2025-07-08 16:38:50,270 - INFO - summary_display - summary_display.py:224 - Successfully displayed interactive last 12 months chart
2025-07-08 16:38:50,272 - INFO - summary_display - summary_display.py:278 - Successfully displayed last 12 months summary metrics
2025-07-08 16:39:49,023 - INFO - summary_display - summary_display.py:197 - Displaying last 12 months generation vs consumption for plant: None
2025-07-08 16:39:49,023 - INFO - summary_display - summary_display.py:202 - Interactive plot setting: True
2025-07-08 16:39:49,124 - WARNING - summary_display - summary_display.py:285 - No data available for last 12 months - None
2025-07-08 16:39:49,124 - INFO - summary_display - summary_display.py:197 - Displaying last 12 months generation vs consumption for plant: Kids Clinic India Limited
2025-07-08 16:39:49,131 - INFO - summary_display - summary_display.py:202 - Interactive plot setting: True
2025-07-08 16:39:49,604 - INFO - summary_display - summary_display.py:212 - Successfully fetched last 12 months data with 6 records
2025-07-08 16:39:49,620 - INFO - summary_display - summary_display.py:224 - Successfully displayed interactive last 12 months chart
2025-07-08 16:39:49,632 - INFO - summary_display - summary_display.py:278 - Successfully displayed last 12 months summary metrics
2025-07-08 16:40:00,901 - INFO - summary_display - summary_display.py:197 - Displaying last 12 months generation vs consumption for plant: Kids Clinic India Limited
2025-07-08 16:40:00,903 - INFO - summary_display - summary_display.py:202 - Interactive plot setting: True
2025-07-08 16:40:01,349 - INFO - summary_display - summary_display.py:212 - Successfully fetched last 12 months data with 6 records
2025-07-08 16:40:01,370 - INFO - summary_display - summary_display.py:224 - Successfully displayed interactive last 12 months chart
2025-07-08 16:40:01,373 - INFO - summary_display - summary_display.py:278 - Successfully displayed last 12 months summary metrics
2025-07-08 16:40:01,377 - INFO - summary_display - summary_display.py:197 - Displaying last 12 months generation vs consumption for plant: Kids Clinic India Limited
2025-07-08 16:40:01,377 - INFO - summary_display - summary_display.py:202 - Interactive plot setting: True
2025-07-08 16:40:01,787 - INFO - summary_display - summary_display.py:212 - Successfully fetched last 12 months data with 6 records
2025-07-08 16:40:01,813 - ERROR - summary_display - summary_display.py:242 - Error generating last 12 months chart: There are multiple `plotly_chart` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `plotly_chart` element.
2025-07-08 16:40:01,815 - INFO - summary_display - summary_display.py:278 - Successfully displayed last 12 months summary metrics
2025-07-08 16:41:10,719 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:41:10,719 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:41:10,958 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 16:41:10,997 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:41:11,274 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 16:41:11,274 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:42:50,332 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:42:50,332 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:42:50,649 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 31 records
2025-07-08 16:42:50,689 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:42:50,924 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 31 records
2025-07-08 16:42:50,924 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:43:03,031 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:43:03,032 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:43:03,546 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:43:03,600 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:43:04,167 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:43:04,167 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:45:00,731 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:45:00,732 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:45:01,324 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:45:01,458 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:45:02,041 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:45:02,043 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:45:13,106 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:45:13,106 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:45:13,692 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:45:13,808 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:45:14,433 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:45:14,433 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:45:39,517 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:45:39,518 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:45:40,072 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:45:40,111 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:45:40,514 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:45:40,514 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:46:25,472 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:46:25,472 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:46:25,985 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:46:26,036 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:46:26,599 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:46:26,599 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:47:15,573 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:47:15,573 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:47:16,051 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:47:16,103 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:47:16,557 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:47:16,559 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:47:43,570 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:47:43,570 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: False
2025-07-08 16:47:43,961 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:47:45,220 - INFO - summary_display - summary_display.py:95 - Successfully displayed static chart
2025-07-08 16:47:45,653 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:47:45,653 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:47:51,680 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:47:51,680 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:47:52,072 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:47:52,169 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:47:52,585 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:47:52,587 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:48:07,144 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:48:07,145 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:48:07,634 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:48:07,704 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:48:08,122 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:48:08,122 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:48:44,224 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:48:44,225 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:48:44,690 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:48:44,742 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:48:45,125 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:48:45,125 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:49:14,173 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:49:14,177 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:49:14,660 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:49:14,712 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:49:15,244 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:49:15,245 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:49:58,111 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:49:58,112 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:49:58,514 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:49:58,564 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:49:59,146 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:49:59,147 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:56:16,402 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:56:16,403 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:56:16,869 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:56:16,912 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:56:17,412 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:56:17,413 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:56:33,974 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:56:33,974 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:56:34,576 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:56:34,675 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:56:35,250 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:56:35,255 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:57:11,582 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:57:11,583 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:57:12,012 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:57:12,051 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:57:12,434 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:57:12,434 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:57:41,031 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:57:41,031 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:57:41,322 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:57:41,323 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: False
2025-07-08 16:57:41,798 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:57:42,014 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:57:43,297 - INFO - summary_display - summary_display.py:95 - Successfully displayed static chart
2025-07-08 16:57:43,670 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:57:43,670 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:57:53,456 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:57:53,456 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: False
2025-07-08 16:57:53,939 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:57:55,183 - INFO - summary_display - summary_display.py:95 - Successfully displayed static chart
2025-07-08 16:57:55,538 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:57:55,538 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:58:00,300 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:58:00,300 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:58:00,852 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:58:00,919 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:58:01,380 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:58:01,380 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:58:30,775 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:58:30,775 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:58:31,349 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:58:31,438 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:58:32,026 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:58:32,027 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:59:44,184 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 16:59:44,186 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 16:59:44,556 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 16:59:44,580 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 16:59:44,943 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 16:59:44,943 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 16:59:46,152 - INFO - summary_display - summary_display.py:217 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 16:59:46,153 - INFO - summary_display - summary_display.py:222 - Interactive plot setting: True
2025-07-08 16:59:46,236 - WARNING - summary_display - summary_display.py:275 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 17:00:54,641 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:00:54,641 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 17:00:55,045 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 17:00:55,083 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 17:00:55,481 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 17:00:55,481 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:00:56,714 - INFO - summary_display - summary_display.py:217 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 17:00:56,714 - INFO - summary_display - summary_display.py:222 - Interactive plot setting: True
2025-07-08 17:00:56,796 - WARNING - summary_display - summary_display.py:275 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 17:01:19,047 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:01:19,047 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 17:01:19,499 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 17:01:19,535 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 17:01:19,884 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 17:01:19,884 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:01:21,147 - INFO - summary_display - summary_display.py:217 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 17:01:21,147 - INFO - summary_display - summary_display.py:222 - Interactive plot setting: True
2025-07-08 17:01:21,230 - WARNING - summary_display - summary_display.py:275 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 17:01:41,125 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:01:41,126 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 17:01:41,600 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 17:01:41,637 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 17:01:41,999 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 17:01:42,001 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:01:43,240 - INFO - summary_display - summary_display.py:217 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 17:01:43,240 - INFO - summary_display - summary_display.py:222 - Interactive plot setting: True
2025-07-08 17:01:43,333 - WARNING - summary_display - summary_display.py:275 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 17:02:07,313 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:02:07,314 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 17:02:07,719 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 17:02:07,761 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 17:02:08,217 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 17:02:08,217 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:02:09,426 - INFO - summary_display - summary_display.py:217 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 17:02:09,427 - INFO - summary_display - summary_display.py:222 - Interactive plot setting: True
2025-07-08 17:02:09,507 - WARNING - summary_display - summary_display.py:275 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 17:02:31,873 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:02:31,873 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 17:02:32,289 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 17:02:32,321 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 17:02:32,702 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 17:02:32,702 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:02:33,856 - INFO - summary_display - summary_display.py:217 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 17:02:33,856 - INFO - summary_display - summary_display.py:222 - Interactive plot setting: True
2025-07-08 17:02:33,960 - WARNING - summary_display - summary_display.py:275 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 17:03:03,971 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:03:03,980 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 17:03:04,422 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 17:03:04,475 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 17:03:04,914 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 17:03:04,914 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:03:05,077 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:03:05,078 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 17:03:05,441 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 17:03:05,514 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 17:03:05,923 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 17:03:05,923 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:03:08,334 - INFO - summary_display - summary_display.py:217 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:03:08,334 - INFO - summary_display - summary_display.py:222 - Interactive plot setting: True
2025-07-08 17:03:08,652 - INFO - summary_display - summary_display.py:237 - Successfully fetched hourly data with 24 records
2025-07-08 17:03:08,758 - INFO - summary_display - summary_display.py:251 - Successfully displayed interactive hourly chart
2025-07-08 17:03:17,249 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:03:17,250 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 17:03:17,692 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 17:03:17,729 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 17:03:18,079 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 17:03:18,079 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:03:19,516 - INFO - summary_display - summary_display.py:217 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:03:19,517 - INFO - summary_display - summary_display.py:222 - Interactive plot setting: True
2025-07-08 17:03:19,694 - INFO - summary_display - summary_display.py:237 - Successfully fetched hourly data with 24 records
2025-07-08 17:03:19,740 - INFO - summary_display - summary_display.py:251 - Successfully displayed interactive hourly chart
2025-07-08 17:08:24,184 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:08:24,184 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:08:24,641 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:08:24,684 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:08:25,050 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:08:25,050 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:08:26,525 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:08:26,526 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:08:26,725 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:08:26,776 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:08:38,892 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:08:38,893 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:08:39,328 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:08:39,366 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:08:39,767 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:08:39,767 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:08:41,234 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:08:41,234 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:08:41,457 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:08:41,492 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:09:42,811 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:09:42,812 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:09:43,301 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:09:43,335 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:09:43,720 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:09:43,720 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:09:45,543 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:09:45,543 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:09:45,785 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:09:45,825 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:10:12,466 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:10:12,466 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:10:12,903 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:10:12,941 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:10:13,319 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:10:13,320 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:10:14,828 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:10:14,829 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:10:15,053 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:10:15,088 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:10:52,183 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:10:52,183 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:10:52,563 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:10:52,840 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:10:53,361 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:10:53,362 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:10:55,307 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 17:10:55,307 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:10:55,424 - WARNING - summary_display - summary_display.py:281 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 17:13:39,225 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:13:39,226 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:13:39,669 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:13:39,706 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:13:40,088 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:13:40,088 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:13:41,592 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:13:41,592 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:13:41,801 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:13:41,840 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:13:41,842 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:13:42,150 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:13:42,150 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:13:42,355 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:13:42,392 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:13:42,393 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:13:43,835 - INFO - summary_display - summary_display.py:531 - Successfully displayed unit-wise monthly bill table with 12 rows
2025-07-08 17:13:43,840 - INFO - summary_display - summary_display.py:540 - Successfully displayed unit-wise summary table with 12 rows
2025-07-08 17:17:19,604 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:17:19,604 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:17:20,046 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:17:20,086 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:17:20,480 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:17:20,480 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:17:22,104 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:17:22,105 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:17:22,317 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:17:22,353 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:17:22,353 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:17:22,661 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:17:22,661 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:17:22,896 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:17:22,919 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:17:22,931 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:17:31,643 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:17:31,643 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:17:32,050 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:17:32,092 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:17:32,482 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:17:32,482 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:17:34,137 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:17:34,138 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:17:34,349 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:17:34,393 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:17:34,397 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:17:34,652 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:17:34,652 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:17:34,839 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:17:34,856 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:17:34,872 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:17:36,149 - INFO - summary_display - summary_display.py:531 - Successfully displayed unit-wise monthly bill table with 12 rows
2025-07-08 17:17:36,149 - INFO - summary_display - summary_display.py:540 - Successfully displayed unit-wise summary table with 12 rows
2025-07-08 17:18:11,786 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:18:11,786 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:18:12,222 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:18:12,255 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:18:12,657 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:18:12,657 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:18:14,146 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:18:14,146 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:18:14,389 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:18:14,440 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:18:14,440 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:18:14,768 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:18:14,768 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:18:14,989 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:18:15,008 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:18:15,015 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:18:26,185 - INFO - summary_display - summary_display.py:50 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:18:26,185 - INFO - summary_display - summary_display.py:55 - Interactive plot setting: True
2025-07-08 17:18:26,659 - INFO - summary_display - summary_display.py:65 - Successfully fetched chart data with 164 records
2025-07-08 17:18:26,692 - INFO - summary_display - summary_display.py:80 - Successfully displayed interactive chart
2025-07-08 17:18:27,073 - INFO - summary_display - summary_display.py:108 - Successfully fetched metrics data with 164 records
2025-07-08 17:18:27,073 - INFO - summary_display - summary_display.py:129 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:18:28,663 - INFO - summary_display - summary_display.py:217 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:18:28,663 - INFO - summary_display - summary_display.py:222 - Interactive plot setting: True
2025-07-08 17:18:28,917 - INFO - summary_display - summary_display.py:237 - Successfully fetched hourly data with 24 records
2025-07-08 17:18:28,964 - INFO - summary_display - summary_display.py:251 - Successfully displayed interactive hourly chart
2025-07-08 17:18:28,964 - INFO - summary_display - summary_display.py:417 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:18:29,266 - INFO - summary_display - summary_display.py:431 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:18:29,266 - INFO - summary_display - summary_display.py:436 - Interactive plot setting: True
2025-07-08 17:18:29,421 - INFO - summary_display - summary_display.py:451 - Successfully fetched pie chart data with 11 records
2025-07-08 17:18:29,442 - INFO - summary_display - summary_display.py:464 - Successfully displayed interactive pie chart
2025-07-08 17:18:29,451 - INFO - summary_display - summary_display.py:497 - Successfully displayed merged table with 11 rows
2025-07-08 17:18:40,219 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:18:40,219 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:18:40,676 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:18:40,716 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:18:41,158 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:18:41,158 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:18:42,608 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:18:42,608 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:18:42,909 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:18:42,944 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:18:42,948 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:18:43,203 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:18:43,203 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:18:43,373 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:18:43,396 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:18:43,401 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:19:04,218 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:19:04,219 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:19:04,677 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:19:04,715 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:19:05,094 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:19:05,094 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:19:06,733 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:19:06,733 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:19:06,959 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:19:07,000 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:19:07,002 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:19:07,277 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:19:07,277 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:19:07,456 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:19:07,478 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:19:07,482 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:19:15,614 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:19:15,615 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:19:15,994 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:19:16,044 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:19:16,488 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:19:16,488 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:19:18,630 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:19:18,630 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:19:18,812 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:19:18,893 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:19:18,909 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:19:19,145 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:19:19,145 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:19:19,327 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:19:19,362 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:19:19,378 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:19:28,239 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:19:28,239 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:19:28,702 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:19:28,735 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:19:29,129 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:19:29,129 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:19:30,699 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:19:30,699 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:19:30,917 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:19:30,961 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:19:30,965 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:19:31,217 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:19:31,226 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:19:31,395 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:19:31,416 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:19:31,450 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:19:31,455 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:19:32,728 - INFO - summary_display - summary_display.py:564 - Successfully displayed unit-wise monthly bill table with 12 rows
2025-07-08 17:19:32,745 - INFO - summary_display - summary_display.py:573 - Successfully displayed unit-wise summary table with 12 rows
2025-07-08 17:20:35,592 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:20:35,592 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:20:36,037 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:20:36,072 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:20:36,502 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:20:36,502 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:20:37,918 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:20:37,919 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:20:38,083 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:20:38,136 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:20:38,139 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:20:38,382 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:20:38,382 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:20:38,548 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:20:38,575 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:20:38,578 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:20:38,581 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:20:39,766 - INFO - summary_display - summary_display.py:564 - Successfully displayed unit-wise summary table with 12 rows
2025-07-08 17:20:48,056 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:20:48,056 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:20:48,474 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:20:48,525 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:20:49,024 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:20:49,024 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:20:50,605 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:20:50,605 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:20:50,834 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:20:50,878 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:20:50,887 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:20:51,167 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:20:51,167 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:20:51,334 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:20:51,356 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:20:51,367 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:20:51,372 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:20:52,551 - INFO - summary_display - summary_display.py:563 - Successfully displayed unit-wise summary table with 12 rows
2025-07-08 17:21:15,146 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:21:15,146 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:21:15,534 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:21:15,572 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:21:15,988 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:21:15,988 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:21:17,507 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:21:17,507 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:21:17,820 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:21:17,860 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:21:17,860 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:21:18,156 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:21:18,156 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:21:18,319 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:21:18,338 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:21:18,341 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:21:18,345 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:21:19,531 - INFO - summary_display - summary_display.py:561 - Successfully displayed unit-wise summary table with 12 rows
2025-07-08 17:21:59,722 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:21:59,722 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:22:00,157 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:22:00,193 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:22:00,555 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:22:00,555 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:22:02,106 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:22:02,106 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:22:02,329 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:22:02,362 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:22:02,362 - INFO - summary_display - summary_display.py:423 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:22:02,662 - INFO - summary_display - summary_display.py:437 - Successfully fetched unitwise data with location, 11 records
2025-07-08 17:22:02,662 - INFO - summary_display - summary_display.py:442 - Interactive plot setting: True
2025-07-08 17:22:02,826 - INFO - summary_display - summary_display.py:457 - Successfully fetched pie chart data with 11 records
2025-07-08 17:22:02,850 - INFO - summary_display - summary_display.py:470 - Successfully displayed interactive pie chart
2025-07-08 17:22:02,854 - INFO - summary_display - summary_display.py:503 - Successfully displayed merged table with 11 rows
2025-07-08 17:22:02,860 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:22:04,213 - INFO - summary_display - summary_display.py:561 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:22:38,516 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:22:38,516 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:22:38,928 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:22:38,969 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:22:39,362 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:22:39,362 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:22:40,897 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:22:40,897 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:22:41,074 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:22:41,111 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:22:41,125 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:22:42,363 - INFO - summary_display - summary_display.py:561 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:22:59,813 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:22:59,814 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:23:00,259 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:23:00,283 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:23:00,682 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:23:00,682 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:23:02,198 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:23:02,198 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:23:02,427 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:23:02,468 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:23:02,468 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:23:03,712 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:28:10,978 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:28:10,979 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:28:11,448 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:28:11,488 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:28:11,937 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:28:11,937 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:28:13,478 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:28:13,478 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:28:13,684 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:28:13,721 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:28:13,724 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:28:14,967 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:29:56,363 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:29:56,363 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:29:56,805 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:29:56,842 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:29:57,226 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:29:57,226 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:29:58,567 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:29:58,568 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:29:58,790 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:29:58,826 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:29:58,826 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:30:00,087 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:30:57,497 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:30:57,497 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:30:57,956 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:30:57,990 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:30:58,361 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:30:58,363 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:30:59,847 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:30:59,847 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:31:00,049 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:31:00,101 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:31:00,104 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:31:01,390 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:40:59,367 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:40:59,370 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:40:59,920 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:41:00,016 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:41:00,680 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:41:00,681 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:41:03,240 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:41:03,241 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:41:03,469 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:41:03,529 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:41:03,546 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:05,849 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:41:15,073 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:41:15,074 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:41:15,705 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:41:15,812 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:41:16,290 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:41:16,292 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:41:18,548 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:41:18,549 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:41:18,770 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:41:18,836 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:41:18,842 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:21,179 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:41:55,615 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:41:55,615 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:41:56,103 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:41:56,129 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:41:56,579 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:41:56,596 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:41:58,147 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:41:58,148 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:41:58,420 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:41:58,463 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:41:58,463 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:41:59,807 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:42:16,895 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:42:16,899 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:42:17,334 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:42:17,371 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:42:17,823 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:42:17,824 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:42:19,290 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:42:19,290 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:42:19,564 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:42:19,649 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:42:19,655 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:42:21,949 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:42:33,579 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:42:33,580 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:42:33,983 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:42:34,022 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:42:34,456 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:42:34,457 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:42:35,904 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:42:35,905 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:42:36,112 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:42:36,149 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:42:36,153 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:42:37,346 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:45:12,921 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:45:12,922 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:45:13,304 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:45:13,331 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:45:13,677 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:45:13,677 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:45:14,750 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 17:45:14,751 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:45:14,876 - WARNING - summary_display - summary_display.py:281 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 17:45:14,876 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 17:45:16,161 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:45:34,725 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:45:34,725 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:45:35,128 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:45:35,168 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:45:35,587 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:45:35,587 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:45:35,793 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:45:35,793 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:45:36,362 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:45:36,401 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:45:36,904 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:45:36,905 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:45:38,318 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:45:38,319 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:45:38,545 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:45:38,586 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:45:38,594 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:45:39,876 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 17:46:55,781 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 17:46:55,783 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 17:46:56,208 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 17:46:56,251 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 17:46:56,651 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 17:46:56,667 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 17:46:58,152 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 17:46:58,152 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 17:46:58,422 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 17:46:58,458 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 17:46:58,458 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 17:46:59,597 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-08 18:02:23,129 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-08 to 2025-07-08
2025-07-08 18:02:23,130 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-08 18:02:23,597 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-08 18:02:23,644 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-08 18:02:24,153 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-08 18:02:24,153 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-08 18:02:25,680 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 18:02:25,680 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-08 18:02:25,912 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-08 18:02:25,958 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-08 18:02:25,961 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 18:02:27,294 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-09 10:33:00,169 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:33:00,170 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:33:00,583 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:33:01,122 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:33:01,586 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:33:01,586 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:33:03,614 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09, hourly_aggregation: False
2025-07-09 10:33:03,614 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 10:33:03,761 - WARNING - summary_display - summary_display.py:281 - No hourly data available for plant Kids Clinic India Limited between 2025-07-09 and 2025-07-09
2025-07-09 10:33:03,765 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:33:06,939 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-09 10:33:07,372 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:33:07,372 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:33:07,910 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:33:07,961 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:33:08,327 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:33:08,327 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:33:10,579 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-09 10:33:10,579 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 10:33:10,776 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-09 10:33:11,725 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-09 10:33:11,739 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-09 10:33:13,887 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-09 10:33:20,892 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:33:20,893 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:33:21,305 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:33:21,383 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:33:21,889 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:33:21,889 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:33:23,856 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09, hourly_aggregation: False
2025-07-09 10:33:23,857 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 10:33:23,937 - WARNING - summary_display - summary_display.py:281 - No hourly data available for plant Kids Clinic India Limited between 2025-07-09 and 2025-07-09
2025-07-09 10:33:23,953 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:33:26,273 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:33:26,274 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:33:26,722 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:33:26,908 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:33:26,908 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:33:27,304 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:33:27,384 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:33:27,821 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:33:27,821 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:33:30,025 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-09 10:33:30,025 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 10:33:30,254 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-09 10:33:30,363 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-09 10:33:30,368 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-09 10:33:32,634 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-09 10:37:24,260 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:37:24,260 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:37:24,639 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:37:24,805 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:37:25,352 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:37:25,354 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:37:26,528 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09, hourly_aggregation: False
2025-07-09 10:37:26,529 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 10:37:26,628 - WARNING - summary_display - summary_display.py:281 - No hourly data available for plant Kids Clinic India Limited between 2025-07-09 and 2025-07-09
2025-07-09 10:37:26,631 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-07-09 to 2025-07-09
2025-07-09 10:37:27,931 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-09 10:37:28,526 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:37:28,526 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:37:28,962 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:37:28,991 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:37:29,419 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:37:29,419 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:37:29,496 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:37:29,497 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:37:29,874 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:37:29,929 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:37:30,333 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:37:30,333 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:37:31,727 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-09 10:37:31,727 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 10:37:31,926 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-09 10:37:32,185 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-09 10:37:32,188 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-09 10:37:33,435 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-09 10:37:55,216 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:37:55,216 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:37:55,674 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:37:55,708 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:37:56,108 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:37:56,108 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:37:57,445 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-09 10:37:57,445 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 10:37:57,655 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-09 10:37:57,693 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-09 10:37:57,705 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-09 10:38:24,824 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:38:24,825 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:38:25,249 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:38:25,510 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:38:26,038 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:38:26,039 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:38:28,064 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:38:28,064 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:38:28,451 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:38:28,527 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:38:28,959 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:38:28,959 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:38:29,096 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 10:38:29,096 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 10:38:29,494 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 10:38:29,561 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 10:38:29,960 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 10:38:29,960 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 10:38:32,298 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-09 10:38:32,298 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 10:38:32,630 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-09 10:38:33,130 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-09 10:38:33,141 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-09 10:38:35,344 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-09 12:04:26,650 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 12:04:26,650 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 12:04:27,688 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 12:04:27,735 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 12:04:28,814 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 12:04:28,814 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 12:04:33,162 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-09 12:04:33,162 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 12:04:33,830 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-09 12:04:33,978 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-09 12:04:33,990 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-09 12:04:37,983 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-09 12:06:40,965 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 12:06:40,965 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 12:06:41,971 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 12:06:42,004 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 12:06:42,989 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 12:06:42,989 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 12:06:45,344 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-09 12:06:45,344 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 12:06:45,850 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-09 12:06:45,889 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-09 12:06:45,901 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-09 12:06:47,512 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
2025-07-09 12:07:13,054 - INFO - summary_display - summary_display.py:56 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2024-07-09 to 2025-07-09
2025-07-09 12:07:13,057 - INFO - summary_display - summary_display.py:61 - Interactive plot setting: True
2025-07-09 12:07:14,157 - INFO - summary_display - summary_display.py:71 - Successfully fetched chart data with 164 records
2025-07-09 12:07:14,193 - INFO - summary_display - summary_display.py:86 - Successfully displayed interactive chart
2025-07-09 12:07:15,189 - INFO - summary_display - summary_display.py:114 - Successfully fetched metrics data with 164 records
2025-07-09 12:07:15,192 - INFO - summary_display - summary_display.py:135 - Calculated metrics - Generation: 4279.35 MWh, Consumption: 2731.72 MWh
2025-07-09 12:07:17,372 - INFO - summary_display - summary_display.py:223 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-09 12:07:17,373 - INFO - summary_display - summary_display.py:228 - Interactive plot setting: True
2025-07-09 12:07:17,870 - INFO - summary_display - summary_display.py:243 - Successfully fetched hourly data with 24 records
2025-07-09 12:07:17,909 - INFO - summary_display - summary_display.py:257 - Successfully displayed interactive hourly chart
2025-07-09 12:07:17,915 - INFO - summary_display - summary_display.py:542 - Displaying unit-wise monthly bill analysis for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-09 12:07:20,840 - INFO - summary_display - summary_display.py:560 - Successfully displayed unit-wise summary table with 78 rows
